(base) Macs-MacBook-Pro:culture_connect mac$ flutter run --debug
Launching lib/main.dart on iPhone15ProMax_iOS17 in debug mode...
flutter: ⚠️ WARNING [2025-06-24T00:59:31.310260] [PerformanceMonitoringService] High memory usage detected {"memory_mb":160.0}
flutter: ⚠️ WARNING [2025-06-24T00:59:46.309091] [PerformanceMonitoringService] High memory usage detected {"memory_mb":158.0}
flutter: 🐛 DEBUG [2025-06-24T00:59:47.173905] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T00:59:47.206745] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T00:59:58.592122] [PerformanceMonitoringService] Slow frame detected {"duration_ms":69}
flutter: 🐛 DEBUG [2025-06-24T00:59:58.740638] [PerformanceMonitoringService] Slow frame detected {"duration_ms":347}
flutter: 🐛 DEBUG [2025-06-24T00:59:58.855660] [PerformanceMonitoringService] Slow frame detected {"duration_ms":82}
flutter: 🐛 DEBUG [2025-06-24T00:59:58.940263] [PerformanceMonitoringService] Slow frame detected {"duration_ms":84}
flutter: 🐛 DEBUG [2025-06-24T00:59:59.109976] [PerformanceMonitoringService] Slow frame detected {"duration_ms":169}
flutter: 🐛 DEBUG [2025-06-24T00:59:59.272111] [PerformanceMonitoringService] Slow frame detected {"duration_ms":148}
flutter: 🐛 DEBUG [2025-06-24T00:59:59.290512] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: ⚠️ WARNING [2025-06-24T01:00:01.314988] [PerformanceMonitoringService] High memory usage detected {"memory_mb":164.0}
flutter: 🐛 DEBUG [2025-06-24T01:00:03.424311] [PerformanceMonitoringService] Slow frame detected {"duration_ms":67}
flutter: 🐛 DEBUG [2025-06-24T01:00:04.695288] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:00:04.957887] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:00:05.232404] [PerformanceMonitoringService] Slow frame detected {"duration_ms":59}
flutter: 🐛 DEBUG [2025-06-24T01:00:05.274212] [PerformanceMonitoringService] Slow frame detected {"duration_ms":40}
flutter: 🐛 DEBUG [2025-06-24T01:00:05.441314] [PerformanceMonitoringService] Slow frame detected {"duration_ms":101}
flutter: 🐛 DEBUG [2025-06-24T01:00:05.476325] [PerformanceMonitoringService] Slow frame detected {"duration_ms":32}
flutter: 🐛 DEBUG [2025-06-24T01:00:05.957021] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-24T01:00:06.007625] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-24T01:00:06.173618] [PerformanceMonitoringService] Slow frame detected {"duration_ms":165}
flutter: 🐛 DEBUG [2025-06-24T01:00:06.251639] [PerformanceMonitoringService] Slow frame detected {"duration_ms":44}
flutter: 🐛 DEBUG [2025-06-24T01:00:06.273814] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-06-24T01:00:06.374332] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-06-24T01:00:06.908141] [PerformanceMonitoringService] Slow frame detected {"duration_ms":451}
flutter: 🐛 DEBUG [2025-06-24T01:00:07.057901] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:00:07.107177] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:00:07.287113] [PerformanceMonitoringService] Slow frame detected {"duration_ms":179}
flutter: 🐛 DEBUG [2025-06-24T01:00:07.307005] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: 🐛 DEBUG [2025-06-24T01:00:07.479069] [PerformanceMonitoringService] Slow frame detected {"duration_ms":72}
flutter: 🐛 DEBUG [2025-06-24T01:00:07.506855] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-06-24T01:00:07.563069] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-06-24T01:00:07.690168] [PerformanceMonitoringService] Slow frame detected {"duration_ms":126}
flutter: 🐛 DEBUG [2025-06-24T01:00:07.873894] [PerformanceMonitoringService] Slow frame detected {"duration_ms":133}
flutter: 🐛 DEBUG [2025-06-24T01:00:08.139618] [PerformanceMonitoringService] Slow frame detected {"duration_ms":132}
flutter: 🐛 DEBUG [2025-06-24T01:00:08.190721] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-24T01:00:08.224588] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:00:08.373549] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:00:08.407851] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:00:08.474133] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:00:08.552681] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: 🐛 DEBUG [2025-06-24T01:00:08.577618] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: 🐛 DEBUG [2025-06-24T01:00:08.722056] [PerformanceMonitoringService] Slow frame detected {"duration_ms":115}
flutter: 🐛 DEBUG [2025-06-24T01:00:08.740481] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: 🐛 DEBUG [2025-06-24T01:00:08.857164] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-06-24T01:00:08.907459] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-24T01:00:16.310715] [PerformanceMonitoringService] High memory usage detected {"memory_mb":160.0}
flutter: 🐛 DEBUG [2025-06-24T01:00:20.607423] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-24T01:00:31.310069] [PerformanceMonitoringService] High memory usage detected {"memory_mb":159.0}
flutter: 🐛 DEBUG [2025-06-24T01:00:35.294074] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: ⚠️ WARNING [2025-06-24T01:00:46.311015] [PerformanceMonitoringService] High memory usage detected {"memory_mb":160.0}
flutter: 🐛 DEBUG [2025-06-24T01:00:50.258356] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:00:54.441739] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-24T01:01:01.311167] [PerformanceMonitoringService] High memory usage detected {"memory_mb":160.0}
flutter: 🐛 DEBUG [2025-06-24T01:01:09.526045] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-24T01:01:16.311766] [PerformanceMonitoringService] High memory usage detected {"memory_mb":161.0}
flutter: 🐛 DEBUG [2025-06-24T01:01:26.558514] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-06-24T01:01:26.576051] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: ⚠️ WARNING [2025-06-24T01:01:31.312869] [PerformanceMonitoringService] High memory usage detected {"memory_mb":162.0}
flutter: 🐛 DEBUG [2025-06-24T01:01:36.242883] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:01:37.826453] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:01:37.859733] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:01:43.094340] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-24T01:01:46.312381] [PerformanceMonitoringService] High memory usage detected {"memory_mb":162.0}
flutter: ⚠️ WARNING [2025-06-24T01:02:01.312783] [PerformanceMonitoringService] High memory usage detected {"memory_mb":162.0}
flutter: ⚠️ WARNING [2025-06-24T01:02:16.313394] [PerformanceMonitoringService] High memory usage detected {"memory_mb":163.0}
flutter: 🐛 DEBUG [2025-06-24T01:02:18.944382] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:28.891441] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-06-24T01:02:28.945165] [PerformanceMonitoringService] Slow frame detected {"duration_ms":53}
flutter: 🐛 DEBUG [2025-06-24T01:02:29.178445] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-06-24T01:02:29.311324] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:29.429070] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-06-24T01:02:29.494763] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-06-24T01:02:29.695511] [PerformanceMonitoringService] Slow frame detected {"duration_ms":166}
flutter: 🐛 DEBUG [2025-06-24T01:02:30.780104] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ⚠️ WARNING [2025-06-24T01:02:31.314057] [PerformanceMonitoringService] High memory usage detected {"memory_mb":163.0}
flutter: 🐛 DEBUG [2025-06-24T01:02:32.694822] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-24T01:02:32.782413] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:33.078732] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:33.128887] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-24T01:02:33.179028] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:33.627886] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-06-24T01:02:33.677927] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:33.814014] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-06-24T01:02:33.845142] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:33.951256] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-24T01:02:34.312191] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-24T01:02:35.528106] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:35.895634] [PerformanceMonitoringService] Slow frame detected {"duration_ms":266}
flutter: 🐛 DEBUG [2025-06-24T01:02:36.377621] [PerformanceMonitoringService] Slow frame detected {"duration_ms":483}
flutter: 🐛 DEBUG [2025-06-24T01:02:36.431133] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-06-24T01:02:36.529151] [PerformanceMonitoringService] Slow frame detected {"duration_ms":96}
flutter: 🐛 DEBUG [2025-06-24T01:02:36.854403] [PerformanceMonitoringService] Slow frame detected {"duration_ms":326}
flutter: 🐛 DEBUG [2025-06-24T01:02:36.894753] [PerformanceMonitoringService] Slow frame detected {"duration_ms":40}
flutter: 🐛 DEBUG [2025-06-24T01:02:36.962417] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-24T01:02:37.101631] [PerformanceMonitoringService] Slow frame detected {"duration_ms":123}
flutter: 🐛 DEBUG [2025-06-24T01:02:37.195822] [PerformanceMonitoringService] Slow frame detected {"duration_ms":92}
flutter: 🐛 DEBUG [2025-06-24T01:02:37.245278] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:37.312971] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-24T01:02:37.344778] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:37.378849] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:37.460893] [PerformanceMonitoringService] Slow frame detected {"duration_ms":78}
flutter: 🐛 DEBUG [2025-06-24T01:02:37.479223] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-06-24T01:02:37.609203] [PerformanceMonitoringService] Slow frame detected {"duration_ms":131}
flutter: 🐛 DEBUG [2025-06-24T01:02:37.629093] [PerformanceMonitoringService] Slow frame detected {"duration_ms":18}
flutter: ⚠️ WARNING [2025-06-24T01:02:46.314627] [PerformanceMonitoringService] High memory usage detected {"memory_mb":164.0}
flutter: 🐛 DEBUG [2025-06-24T01:02:50.895400] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:50.928497] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:51.761867] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-24T01:02:51.795104] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:52.028366] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:52.428979] [PerformanceMonitoringService] Slow frame detected {"duration_ms":400}
flutter: 🐛 DEBUG [2025-06-24T01:02:52.545929] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-06-24T01:02:52.761996] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-24T01:02:52.822763] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:52.873702] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-24T01:02:52.900186] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:53.012186] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:53.162696] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:53.261988] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:53.345517] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-24T01:02:53.412760] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:53.580815] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-06-24T01:02:53.678709] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:54.628574] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:54.812207] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:54.946181] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-24T01:02:55.295346] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: 🐛 DEBUG [2025-06-24T01:02:57.228833] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:58.912495] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:02:59.730439] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ⚠️ WARNING [2025-06-24T01:03:01.314445] [PerformanceMonitoringService] High memory usage detected {"memory_mb":164.0}
flutter: 🐛 DEBUG [2025-06-24T01:03:03.329025] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:03:03.379080] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:03:03.446461] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-24T01:03:12.763441] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-24T01:03:16.315887] [PerformanceMonitoringService] High memory usage detected {"memory_mb":165.0}
flutter: ⚠️ WARNING [2025-06-24T01:03:31.315859] [PerformanceMonitoringService] High memory usage detected {"memory_mb":165.0}
flutter: 🐛 DEBUG [2025-06-24T01:03:33.913778] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:03:33.946511] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:03:35.587679] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-06-24T01:03:35.613698] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: 🐛 DEBUG [2025-06-24T01:03:35.879799] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:03:37.046455] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-06-24T01:03:45.463539] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:03:46.130857] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:03:46.181057] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-24T01:03:46.214737] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-24T01:03:46.316600] [PerformanceMonitoringService] High memory usage detected {"memory_mb":166.0}
flutter: 🐛 DEBUG [2025-06-24T01:03:53.741536] [PerformanceMonitoringService] Slow frame detected {"duration_ms":258}
flutter: 🐛 DEBUG [2025-06-24T01:03:53.764217] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-06-24T01:03:53.897359] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-06-24T01:03:54.214146] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-24T01:03:54.291713] [PerformanceMonitoringService] Slow frame detected {"duration_ms":78}
flutter: 🐛 DEBUG [2025-06-24T01:03:54.314119] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-06-24T01:03:54.401332] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: 🐛 DEBUG [2025-06-24T01:03:54.430841] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-06-24T01:03:54.997473] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:03:55.125857] [PerformanceMonitoringService] Slow frame detected {"duration_ms":128}
flutter: 🐛 DEBUG [2025-06-24T01:03:55.147151] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-06-24T01:03:55.430790] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-06-24T01:03:55.680681] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:03:55.814243] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:03:58.619912] [PerformanceMonitoringService] Slow frame detected {"duration_ms":273}
flutter: 🐛 DEBUG [2025-06-24T01:03:58.647760] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-06-24T01:03:58.875926] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-06-24T01:03:59.097448] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: ⚠️ WARNING [2025-06-24T01:04:01.316997] [PerformanceMonitoringService] High memory usage detected {"memory_mb":166.0}
flutter: ⚠️ WARNING [2025-06-24T01:04:16.322524] [PerformanceMonitoringService] High memory usage detected {"memory_mb":172.0}
flutter: ⚠️ WARNING [2025-06-24T01:04:31.317949] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: 🐛 DEBUG [2025-06-24T01:04:34.731800] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-06-24T01:04:36.048345] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-24T01:04:46.317924] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: 🐛 DEBUG [2025-06-24T01:04:47.084988] [PerformanceMonitoringService] Slow frame detected {"duration_ms":53}
flutter: 🐛 DEBUG [2025-06-24T01:04:47.332180] [PerformanceMonitoringService] Slow frame detected {"duration_ms":246}
flutter: 🐛 DEBUG [2025-06-24T01:04:47.432153] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-06-24T01:04:47.543238] [PerformanceMonitoringService] Slow frame detected {"duration_ms":94}
flutter: 🐛 DEBUG [2025-06-24T01:04:47.566043] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-06-24T01:04:47.782433] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:04:47.882071] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:04:48.014316] [PerformanceMonitoringService] Slow frame detected {"duration_ms":48}
flutter: 🐛 DEBUG [2025-06-24T01:04:48.032730] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: ⚠️ WARNING [2025-06-24T01:05:01.319501] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: 🐛 DEBUG [2025-06-24T01:05:03.515883] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:05:03.593830] [PerformanceMonitoringService] Slow frame detected {"duration_ms":78}
flutter: 🐛 DEBUG [2025-06-24T01:05:03.616112] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-06-24T01:05:03.882543] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:05:03.966664] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:05:04.032514] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-24T01:05:11.232808] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:05:11.383168] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-24T01:05:16.319384] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: 🐛 DEBUG [2025-06-24T01:05:17.216297] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:05:17.438030] [PerformanceMonitoringService] Slow frame detected {"duration_ms":205}
flutter: 🐛 DEBUG [2025-06-24T01:05:17.738840] [PerformanceMonitoringService] Slow frame detected {"duration_ms":294}
flutter: 🐛 DEBUG [2025-06-24T01:05:17.798513] [PerformanceMonitoringService] Slow frame detected {"duration_ms":65}
flutter: 🐛 DEBUG [2025-06-24T01:05:17.817563] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: 🐛 DEBUG [2025-06-24T01:05:28.767] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:05:28.960057] [PerformanceMonitoringService] Slow frame detected {"duration_ms":93}
flutter: 🐛 DEBUG [2025-06-24T01:05:29.049840] [PerformanceMonitoringService] Slow frame detected {"duration_ms":89}
flutter: ⚠️ WARNING [2025-06-24T01:05:31.319215] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:05:46.338673] [PerformanceMonitoringService] High memory usage detected {"memory_mb":188.0}
flutter: 🐛 DEBUG [2025-06-24T01:05:48.284460] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-24T01:06:01.321530] [PerformanceMonitoringService] High memory usage detected {"memory_mb":171.0}
flutter: 🐛 DEBUG [2025-06-24T01:06:08.734481] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-24T01:06:16.319923] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:06:31.309117] [PerformanceMonitoringService] High memory usage detected {"memory_mb":158.0}
flutter: ⚠️ WARNING [2025-06-24T01:06:36.300155] [PerformanceMonitoringService] High memory usage detected {"memory_mb":199.0}
flutter: ⚠️ WARNING [2025-06-24T01:06:51.286339] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: ⚠️ WARNING [2025-06-24T01:07:06.281561] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
flutter: ⚠️ WARNING [2025-06-24T01:07:21.279225] [PerformanceMonitoringService] High memory usage detected {"memory_mb":179.0}
flutter: 🐛 DEBUG [2025-06-24T01:07:25.548292] [PerformanceMonitoringService] Slow frame detected {"duration_ms":4233}
flutter: 🐛 DEBUG [2025-06-24T01:07:25.826472] [PerformanceMonitoringService] Slow frame detected {"duration_ms":283}
flutter: 🐛 DEBUG [2025-06-24T01:07:25.976388] [PerformanceMonitoringService] Slow frame detected {"duration_ms":150}
flutter: 🐛 DEBUG [2025-06-24T01:07:26.209933] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-06-24T01:07:26.299986] [PerformanceMonitoringService] Slow frame detected {"duration_ms":90}
flutter: 🐛 DEBUG [2025-06-24T01:07:26.328349] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-06-24T01:07:26.411270] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-24T01:07:26.437155] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:07:26.614798] [PerformanceMonitoringService] Slow frame detected {"duration_ms":188}
flutter: 🐛 DEBUG [2025-06-24T01:07:26.701017] [PerformanceMonitoringService] Slow frame detected {"duration_ms":78}
flutter: 🐛 DEBUG [2025-06-24T01:07:26.947613] [PerformanceMonitoringService] Slow frame detected {"duration_ms":221}
flutter: 🐛 DEBUG [2025-06-24T01:07:27.044450] [PerformanceMonitoringService] Slow frame detected {"duration_ms":95}
flutter: 🐛 DEBUG [2025-06-24T01:07:27.143080] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:07:27.375179] [PerformanceMonitoringService] Slow frame detected {"duration_ms":82}
flutter: 🐛 DEBUG [2025-06-24T01:07:27.413780] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: 🐛 DEBUG [2025-06-24T01:07:27.576393] [PerformanceMonitoringService] Slow frame detected {"duration_ms":183}
flutter: 🐛 DEBUG [2025-06-24T01:07:27.726649] [PerformanceMonitoringService] Slow frame detected {"duration_ms":150}
flutter: 🐛 DEBUG [2025-06-24T01:07:27.865161] [PerformanceMonitoringService] Slow frame detected {"duration_ms":138}
flutter: 🐛 DEBUG [2025-06-24T01:07:27.962822] [PerformanceMonitoringService] Slow frame detected {"duration_ms":94}
flutter: 🐛 DEBUG [2025-06-24T01:07:28.007542] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-06-24T01:07:28.060643] [PerformanceMonitoringService] Slow frame detected {"duration_ms":63}
flutter: 🐛 DEBUG [2025-06-24T01:07:28.149821] [PerformanceMonitoringService] Slow frame detected {"duration_ms":72}
flutter: 🐛 DEBUG [2025-06-24T01:07:28.176352] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-06-24T01:07:28.259114] [PerformanceMonitoringService] Slow frame detected {"duration_ms":82}
flutter: 🐛 DEBUG [2025-06-24T01:07:28.376508] [PerformanceMonitoringService] Slow frame detected {"duration_ms":117}
flutter: 🐛 DEBUG [2025-06-24T01:07:28.463308] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-24T01:07:28.513743] [PerformanceMonitoringService] Slow frame detected {"duration_ms":54}
flutter: 🐛 DEBUG [2025-06-24T01:07:28.543056] [PerformanceMonitoringService] Slow frame detected {"duration_ms":29}
flutter: 🐛 DEBUG [2025-06-24T01:07:28.595765] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:07:28.626341] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:07:28.660212] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:07:28.839681] [PerformanceMonitoringService] Slow frame detected {"duration_ms":146}
flutter: 🐛 DEBUG [2025-06-24T01:07:28.860541] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-06-24T01:07:29.007168] [PerformanceMonitoringService] Slow frame detected {"duration_ms":147}
flutter: 🐛 DEBUG [2025-06-24T01:07:29.026491] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-06-24T01:07:29.109825] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-24T01:07:29.326881] [PerformanceMonitoringService] Slow frame detected {"duration_ms":184}
flutter: 🐛 DEBUG [2025-06-24T01:07:29.399051] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-06-24T01:07:29.426465] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-06-24T01:07:29.493911] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-24T01:07:30.243422] [PerformanceMonitoringService] Slow frame detected {"duration_ms":683}
flutter: 🐛 DEBUG [2025-06-24T01:07:30.328416] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-06-24T01:07:30.476292] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-24T01:07:30.509855] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:07:30.676673] [PerformanceMonitoringService] Slow frame detected {"duration_ms":149}
flutter: 🐛 DEBUG [2025-06-24T01:07:31.209838] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: ⚠️ WARNING [2025-06-24T01:07:31.306358] [PerformanceMonitoringService] High memory usage detected {"memory_mb":156.0}
flutter: 🐛 DEBUG [2025-06-24T01:07:31.310487] [PerformanceMonitoringService] Slow frame detected {"duration_ms":84}
flutter: 🐛 DEBUG [2025-06-24T01:07:31.359556] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:07:31.446870] [PerformanceMonitoringService] Slow frame detected {"duration_ms":54}
flutter: 🐛 DEBUG [2025-06-24T01:07:31.510919] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-24T01:07:31.567001] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:07:31.729271] [PerformanceMonitoringService] Slow frame detected {"duration_ms":166}
flutter: 🐛 DEBUG [2025-06-24T01:07:31.942860] [PerformanceMonitoringService] Slow frame detected {"duration_ms":216}
flutter: 🐛 DEBUG [2025-06-24T01:07:32.419263] [PerformanceMonitoringService] Slow frame detected {"duration_ms":470}
flutter: 🐛 DEBUG [2025-06-24T01:07:32.609878] [PerformanceMonitoringService] Slow frame detected {"duration_ms":145}
flutter: 🐛 DEBUG [2025-06-24T01:07:32.615353] [PerformanceMonitoringService] Slow frame detected {"duration_ms":55}
flutter: 🐛 DEBUG [2025-06-24T01:07:32.642873] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-06-24T01:07:32.832984] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-06-24T01:07:32.860089] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-06-24T01:07:32.986924] [PerformanceMonitoringService] Slow frame detected {"duration_ms":94}
flutter: 🐛 DEBUG [2025-06-24T01:07:33.010375] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: 🐛 DEBUG [2025-06-24T01:07:33.460927] [PerformanceMonitoringService] Slow frame detected {"duration_ms":273}
flutter: 🐛 DEBUG [2025-06-24T01:07:33.466230] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-24T01:07:33.562100] [PerformanceMonitoringService] Slow frame detected {"duration_ms":93}
flutter: 🐛 DEBUG [2025-06-24T01:07:33.643331] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:07:33.742894] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-24T01:07:33.910918] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:07:33.993045] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:07:35.059717] [PerformanceMonitoringService] Slow frame detected {"duration_ms":133}
flutter: 🐛 DEBUG [2025-06-24T01:07:35.494149] [PerformanceMonitoringService] Slow frame detected {"duration_ms":84}
flutter: 🐛 DEBUG [2025-06-24T01:07:35.593004] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-06-24T01:07:35.626361] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-24T01:07:36.279168] [PerformanceMonitoringService] High memory usage detected {"memory_mb":179.0}
flutter: 🐛 DEBUG [2025-06-24T01:07:36.431156] [PerformanceMonitoringService] Slow frame detected {"duration_ms":38}
flutter: 🐛 DEBUG [2025-06-24T01:07:36.459796] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-06-24T01:07:48.243250] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-24T01:07:51.279538] [PerformanceMonitoringService] High memory usage detected {"memory_mb":179.0}
flutter: ⚠️ WARNING [2025-06-24T01:08:06.284145] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: ⚠️ WARNING [2025-06-24T01:08:21.281828] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
flutter: ⚠️ WARNING [2025-06-24T01:08:51.279701] [PerformanceMonitoringService] High memory usage detected {"memory_mb":179.0}
flutter: ⚠️ WARNING [2025-06-24T01:09:06.279987] [PerformanceMonitoringService] High memory usage detected {"memory_mb":179.0}
flutter: ⚠️ WARNING [2025-06-24T01:09:21.280815] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: ⚠️ WARNING [2025-06-24T01:09:36.280778] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: ⚠️ WARNING [2025-06-24T01:09:51.281275] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
Running Xcode build...                                                  
flutter: ⚠️ WARNING [2025-06-24T01:10:06.281367] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
flutter: 🐛 DEBUG [2025-06-24T01:10:10.995382] [PerformanceMonitoringService] Slow frame detected {"duration_ms":2116}
 └─Compiling, linking and signing...                        41.7s
Xcode build done.                                           474.9s
flutter: ⚠️ WARNING [2025-06-24T01:10:36.281866] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
no valid “aps-environment” entitlement string found for application
flutter: 🚀 Starting app initialization
flutter: ✅ SharedPreferences initialized in 52ms
flutter: ❌ Error preloading asset assets/images/splash.png: Unable to load asset: "assets/images/splash.png".
flutter: The asset does not exist or has empty data.
flutter: ✅ Preloaded asset: assets/animations/splash_animation.json (3741 bytes)
flutter: ✅ Critical assets preloaded in 100ms
flutter: ✅ Firebase core initialized in 488ms
flutter: ✅ App initialization completed in 510ms
flutter: ℹ️ INFO [2025-06-24T01:10:59.121693] [LoggingService] Logging service initialized successfully
flutter: ℹ️ INFO [2025-06-24T01:10:59.127548] [LoggingService] Device info: {name: iPhone15ProMax_iOS17, model: iPhone, systemName: iOS, systemVersion: 17.2, platform: ios}
flutter: ℹ️ INFO [2025-06-24T01:10:59.128399] [LoggingService] App info: Culture Connect 1.0.0+1
flutter: ✅ Preloaded asset: assets/images/onboarding_1.png (0 bytes)
flutter: ✅ Firebase full features initialized in 141ms
flutter: ℹ️ INFO [2025-06-24T01:10:59.248748] [ErrorHandlingService] Error handling service initialized
flutter: ✅ Preloaded asset: assets/images/onboarding_2.png (0 bytes)
flutter: ✅ Preloaded asset: assets/images/onboarding_3.png (0 bytes)
flutter: ✅ Non-critical assets preloaded in 152ms
flutter: ℹ️ INFO [2025-06-24T01:10:59.272493] [CrashReportingService] Crash reporting service initialized
flutter: ℹ️ INFO [2025-06-24T01:10:59.295619] [AnalyticsService] Analytics service initialized
flutter: ⚠️ WARNING [2025-06-24T01:10:59.306401] [Error[AnalyticsService.logEvent]] AR feature error. Please ensure your device supports AR and try again. {"error":"Invalid argument (name): Event name is reserved and cannot be used: \"session_start\"","type":"ar","severity":"low"}
flutter: Stack trace:
flutter: #0      _logEventNameValidation (package:firebase_analytics/src/firebase_analytics.dart:1474:5)
flutter: #1      FirebaseAnalytics.logEvent (package:firebase_analytics/src/firebase_analytics.dart:117:5)
flutter: #2      AnalyticsService.logEvent (package:culture_connect/services/analytics_service.dart:278:24)
flutter: #3      AnalyticsService._startSession (package:culture_connect/services/analytics_service.dart:115:5)
flutter: #4      AnalyticsService.initialize (package:culture_connect/services/analytics_service.dart:73:7)
flutter: <asynchronous suspension>
flutter: #5      _initializeServices (package:culture_connect/main.dart:113:5)
flutter: <asynchronous suspension>
flutter: #6      main (package:culture_connect/main.dart:85:3)
flutter: <asynchronous suspension>
flutter:
flutter: ℹ️ INFO [2025-06-24T01:10:59.324219] [PerformanceMonitoringService] Performance monitoring service initialized
flutter: ℹ️ INFO [2025-06-24T01:10:59.324676] [App] All services initialized successfully
flutter: 🐛 DEBUG [2025-06-24T01:11:01.203194] [PerformanceMonitoringService] Slow frame detected {"duration_ms":179}
flutter: 🐛 DEBUG [2025-06-24T01:11:01.247226] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: 🐛 DEBUG [2025-06-24T01:11:01.353481] [PerformanceMonitoringService] Slow frame detected {"duration_ms":57}
flutter: 🐛 DEBUG [2025-06-24T01:11:01.381098] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-06-24T01:11:01.550852] [PerformanceMonitoringService] Slow frame detected {"duration_ms":70}
flutter: 🐛 DEBUG [2025-06-24T01:11:01.629698] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:11:01.689133] [PerformanceMonitoringService] Slow frame detected {"duration_ms":59}
flutter: 🐛 DEBUG [2025-06-24T01:11:01.713648] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-06-24T01:11:01.907940] [PerformanceMonitoringService] Slow frame detected {"duration_ms":77}
flutter: 🐛 DEBUG [2025-06-24T01:11:01.930170] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: ❌ ERROR [2025-06-24T01:11:03.034282] [FlutterError] 'package:flutter_riverpod/src/consumer.dart': Failed assertion: line 600 pos 7: 'debugDoingBuild': ref.listen can only be used within the build method of a ConsumerWidget 'package:flutter_riverpod/src/consumer.dart': Failed assertion: line 600 pos 7: 'debugDoingBuild': ref.listen can only be used within the build method of a ConsumerWidget
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      ConsumerStatefulElement.listen (package:flutter_riverpod/src/consumer.dart:600:7)
flutter: #3      _SplashScreenState._checkInitializationStatus (package:culture_connect/screens/splash_screen.dart:45:9)
flutter: #4      _SplashScreenState.initState.<anonymous closure> (package:culture_connect/screens/splash_screen.dart:38:9)
flutter: #5      AnimationLocalStatusListenersMixin.notifyStatusListeners (package:flutter/src/animation/listener_helpers.dart:240:19)
flutter: #6      AnimationController._checkStatusChanged (package:flutter/src/animation/animation_controller.dart:906:7)
flutter: #7      AnimationController._tick (package:flutter/src/animation/animation_controller.dart:922:5)
flutter: #8      Ticker._tick (package:flutter/src/scheduler/ticker.dart:275:12)
flutter: #9      SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #10     SchedulerBinding.handleBeginFrame.<anonymous closure> (package:flutter/src/scheduler/binding.dart:1240:11)
flutter: #11     _LinkedHashMapMixin.forEach (dart:_compact_hash:726:13)
flutter: #12     SchedulerBinding.handleBeginFrame (package:flutter/src/scheduler/binding.dart:1238:17)
flutter: #13     SchedulerBinding._handleBeginFrame (package:flutter/src/scheduler/binding.dart:1155:5)
flutter: #14     _invoke1 (dart:ui/hooks.dart:328:13)
flutter: #15     PlatformDispatcher._beginFrame (dart:ui/platform_dispatcher.dart:405:5)
flutter: #16     _beginFrame (dart:ui/hooks.dart:272:31)
flutter:
flutter: 🐛 DEBUG [2025-06-24T01:11:03.063318] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
Syncing files to device iPhone15ProMax_iOS17...                     5.0s

Flutter run key commands.
r Hot reload. 🔥🔥🔥
R Hot restart.
h List all available interactive commands.
d Detach (terminate "flutter run" but leave application running).
c Clear the screen
q Quit (terminate the application on the device).

A Dart VM Service on iPhone15ProMax_iOS17 is available at: http://127.0.0.1:55898/iNfTv28mzdk=/
The Flutter DevTools debugger and profiler on iPhone15ProMax_iOS17 is available at:
http://127.0.0.1:9101?uri=http://127.0.0.1:55898/iNfTv28mzdk=/
flutter: ⚠️ WARNING [2025-06-24T01:11:04.324879] [PerformanceMonitoringService] High memory usage detected {"memory_mb":174.0}
flutter: ⚠️ WARNING [2025-06-24T01:11:19.325949] [PerformanceMonitoringService] High memory usage detected {"memory_mb":175.0}
flutter: ⚠️ WARNING [2025-06-24T01:11:34.326483] [PerformanceMonitoringService] High memory usage detected {"memory_mb":175.0}
flutter: ⚠️ WARNING [2025-06-24T01:11:49.324879] [PerformanceMonitoringService] High memory usage detected {"memory_mb":174.0}
flutter: ⚠️ WARNING [2025-06-24T01:12:04.326765] [PerformanceMonitoringService] High memory usage detected {"memory_mb":176.0}
flutter: ⚠️ WARNING [2025-06-24T01:12:19.326282] [PerformanceMonitoringService] High memory usage detected {"memory_mb":176.0}
flutter: ⚠️ WARNING [2025-06-24T01:12:34.327041] [PerformanceMonitoringService] High memory usage detected {"memory_mb":176.0}
flutter: ⚠️ WARNING [2025-06-24T01:12:49.327847] [PerformanceMonitoringService] High memory usage detected {"memory_mb":177.0}
flutter: ⚠️ WARNING [2025-06-24T01:13:04.326476] [PerformanceMonitoringService] High memory usage detected {"memory_mb":176.0}
flutter: ⚠️ WARNING [2025-06-24T01:13:19.328886] [PerformanceMonitoringService] High memory usage detected {"memory_mb":178.0}
flutter: ⚠️ WARNING [2025-06-24T01:13:34.327868] [PerformanceMonitoringService] High memory usage detected {"memory_mb":177.0}
flutter: ⚠️ WARNING [2025-06-24T01:13:49.329858] [PerformanceMonitoringService] High memory usage detected {"memory_mb":179.0}
flutter: ⚠️ WARNING [2025-06-24T01:14:04.329244] [PerformanceMonitoringService] High memory usage detected {"memory_mb":179.0}
flutter: ⚠️ WARNING [2025-06-24T01:14:19.330235] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: ⚠️ WARNING [2025-06-24T01:14:34.330329] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: ⚠️ WARNING [2025-06-24T01:14:49.328462] [PerformanceMonitoringService] High memory usage detected {"memory_mb":178.0}
flutter: ⚠️ WARNING [2025-06-24T01:15:04.330740] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: ⚠️ WARNING [2025-06-24T01:15:19.330177] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: ⚠️ WARNING [2025-06-24T01:15:34.329617] [PerformanceMonitoringService] High memory usage detected {"memory_mb":179.0}
flutter: 🐛 DEBUG [2025-06-24T01:15:46.536696] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: 🐛 DEBUG [2025-06-24T01:15:46.709165] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ⚠️ WARNING [2025-06-24T01:15:49.330226] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: ⚠️ WARNING [2025-06-24T01:16:04.329764] [PerformanceMonitoringService] High memory usage detected {"memory_mb":179.0}
flutter: ⚠️ WARNING [2025-06-24T01:16:19.331383] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
flutter: ⚠️ WARNING [2025-06-24T01:16:34.330226] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: ⚠️ WARNING [2025-06-24T01:16:49.332225] [PerformanceMonitoringService] High memory usage detected {"memory_mb":182.0}
flutter: ⚠️ WARNING [2025-06-24T01:17:04.332442] [PerformanceMonitoringService] High memory usage detected {"memory_mb":182.0}
flutter: ⚠️ WARNING [2025-06-24T01:17:19.333184] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: ⚠️ WARNING [2025-06-24T01:17:34.333134] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: ⚠️ WARNING [2025-06-24T01:17:49.334154] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: ⚠️ WARNING [2025-06-24T01:18:04.331803] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
flutter: ⚠️ WARNING [2025-06-24T01:18:19.332931] [PerformanceMonitoringService] High memory usage detected {"memory_mb":182.0}
flutter: ⚠️ WARNING [2025-06-24T01:18:34.334055] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: ⚠️ WARNING [2025-06-24T01:18:49.334509] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: ⚠️ WARNING [2025-06-24T01:19:04.334416] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: ⚠️ WARNING [2025-06-24T01:19:19.334625] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: ⚠️ WARNING [2025-06-24T01:19:34.335435] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: ⚠️ WARNING [2025-06-24T01:19:49.336250] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: ⚠️ WARNING [2025-06-24T01:20:04.334411] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: ⚠️ WARNING [2025-06-24T01:20:19.336478] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: ⚠️ WARNING [2025-06-24T01:20:34.335889] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: ⚠️ WARNING [2025-06-24T01:20:49.336436] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: ⚠️ WARNING [2025-06-24T01:21:04.336013] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: ⚠️ WARNING [2025-06-24T01:21:19.336276] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: ⚠️ WARNING [2025-06-24T01:21:34.337686] [PerformanceMonitoringService] High memory usage detected {"memory_mb":187.0}
flutter: ⚠️ WARNING [2025-06-24T01:21:49.337376] [PerformanceMonitoringService] High memory usage detected {"memory_mb":187.0}
flutter: ⚠️ WARNING [2025-06-24T01:22:04.339055] [PerformanceMonitoringService] High memory usage detected {"memory_mb":188.0}
flutter: ⚠️ WARNING [2025-06-24T01:22:19.336934] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: ⚠️ WARNING [2025-06-24T01:22:34.338715] [PerformanceMonitoringService] High memory usage detected {"memory_mb":188.0}
flutter: ⚠️ WARNING [2025-06-24T01:22:49.338530] [PerformanceMonitoringService] High memory usage detected {"memory_mb":188.0}
flutter: ⚠️ WARNING [2025-06-24T01:23:04.338948] [PerformanceMonitoringService] High memory usage detected {"memory_mb":188.0}
flutter: ⚠️ WARNING [2025-06-24T01:23:19.339345] [PerformanceMonitoringService] High memory usage detected {"memory_mb":189.0}
flutter: ⚠️ WARNING [2025-06-24T01:23:34.340276] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: ⚠️ WARNING [2025-06-24T01:23:49.339438] [PerformanceMonitoringService] High memory usage detected {"memory_mb":189.0}
flutter: ⚠️ WARNING [2025-06-24T01:24:04.340090] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: ⚠️ WARNING [2025-06-24T01:24:19.341553] [PerformanceMonitoringService] High memory usage detected {"memory_mb":191.0}
flutter: ⚠️ WARNING [2025-06-24T01:24:34.340478] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: ⚠️ WARNING [2025-06-24T01:24:49.341716] [PerformanceMonitoringService] High memory usage detected {"memory_mb":191.0}
flutter: ⚠️ WARNING [2025-06-24T01:25:04.341672] [PerformanceMonitoringService] High memory usage detected {"memory_mb":191.0}
flutter: ⚠️ WARNING [2025-06-24T01:25:19.342450] [PerformanceMonitoringService] High memory usage detected {"memory_mb":192.0}
flutter: ⚠️ WARNING [2025-06-24T01:25:34.341544] [PerformanceMonitoringService] High memory usage detected {"memory_mb":191.0}
flutter: ⚠️ WARNING [2025-06-24T01:25:49.343791] [PerformanceMonitoringService] High memory usage detected {"memory_mb":193.0}
flutter: ⚠️ WARNING [2025-06-24T01:26:04.342046] [PerformanceMonitoringService] High memory usage detected {"memory_mb":191.0}
flutter: ⚠️ WARNING [2025-06-24T01:26:19.341845] [PerformanceMonitoringService] High memory usage detected {"memory_mb":191.0}
flutter: ⚠️ WARNING [2025-06-24T01:26:34.343903] [PerformanceMonitoringService] High memory usage detected {"memory_mb":193.0}
flutter: ⚠️ WARNING [2025-06-24T01:26:39.266619] [PerformanceMonitoringService] High memory usage detected {"memory_mb":166.0}
flutter: ⚠️ WARNING [2025-06-24T01:26:54.267973] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: ⚠️ WARNING [2025-06-24T01:27:09.267834] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: ⚠️ WARNING [2025-06-24T01:27:24.267952] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: ⚠️ WARNING [2025-06-24T01:27:39.268433] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:27:54.268930] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:28:09.267374] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: ⚠️ WARNING [2025-06-24T01:28:24.268487] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:28:39.267124] [PerformanceMonitoringService] High memory usage detected {"memory_mb":166.0}
flutter: ⚠️ WARNING [2025-06-24T01:28:54.268688] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:29:09.268289] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:29:24.267678] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: ⚠️ WARNING [2025-06-24T01:29:39.267870] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: ⚠️ WARNING [2025-06-24T01:29:54.266516] [PerformanceMonitoringService] High memory usage detected {"memory_mb":166.0}
flutter: ⚠️ WARNING [2025-06-24T01:30:09.267765] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: ⚠️ WARNING [2025-06-24T01:30:24.268052] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: ⚠️ WARNING [2025-06-24T01:30:39.268828] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:30:54.268539] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:31:09.269237] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:31:24.268273] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:31:39.269690] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:31:54.267467] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: ⚠️ WARNING [2025-06-24T01:32:09.268158] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:32:24.266978] [PerformanceMonitoringService] High memory usage detected {"memory_mb":166.0}
flutter: ⚠️ WARNING [2025-06-24T01:32:39.269299] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:32:54.269831] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:33:09.267568] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: ⚠️ WARNING [2025-06-24T01:33:24.269115] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:33:39.269353] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:33:54.267338] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: ⚠️ WARNING [2025-06-24T01:34:09.269148] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:34:24.269478] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:34:39.268575] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:34:54.269077] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:35:09.268944] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:35:24.269531] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:35:39.268416] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:35:54.270136] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:36:09.268109] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:36:24.268862] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:36:39.269844] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:36:54.269869] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:37:09.268895] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:37:24.269882] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:37:39.269034] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:37:54.268630] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-24T01:38:09.269474] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:38:24.269362] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:38:39.269869] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:38:54.270430] [PerformanceMonitoringService] High memory usage detected {"memory_mb":170.0}
flutter: ⚠️ WARNING [2025-06-24T01:39:09.270807] [PerformanceMonitoringService] High memory usage detected {"memory_mb":170.0}
flutter: ⚠️ WARNING [2025-06-24T01:39:24.270801] [PerformanceMonitoringService] High memory usage detected {"memory_mb":170.0}
flutter: ⚠️ WARNING [2025-06-24T01:39:39.270988] [PerformanceMonitoringService] High memory usage detected {"memory_mb":170.0}
flutter: ⚠️ WARNING [2025-06-24T01:39:54.270634] [PerformanceMonitoringService] High memory usage detected {"memory_mb":170.0}
flutter: ⚠️ WARNING [2025-06-24T01:40:09.269517] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:40:24.269434] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:40:39.270116] [PerformanceMonitoringService] High memory usage detected {"memory_mb":170.0}
flutter: ⚠️ WARNING [2025-06-24T01:40:54.270424] [PerformanceMonitoringService] High memory usage detected {"memory_mb":170.0}
flutter: ⚠️ WARNING [2025-06-24T01:41:09.269296] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:41:24.271011] [PerformanceMonitoringService] High memory usage detected {"memory_mb":170.0}
flutter: ⚠️ WARNING [2025-06-24T01:41:39.269676] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:41:54.269588] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:42:09.270252] [PerformanceMonitoringService] High memory usage detected {"memory_mb":170.0}
flutter: ⚠️ WARNING [2025-06-24T01:42:24.270751] [PerformanceMonitoringService] High memory usage detected {"memory_mb":170.0}
flutter: ⚠️ WARNING [2025-06-24T01:42:39.269591] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-24T01:42:54.270360] [PerformanceMonitoringService] High memory usage detected {"memory_mb":170.0}
flutter: ⚠️ WARNING [2025-06-24T01:43:09.271376] [PerformanceMonitoringService] High memory usage detected {"memory_mb":171.0}
flutter: ⚠️ WARNING [2025-06-24T01:43:24.271264] [PerformanceMonitoringService] High memory usage detected {"memory_mb":171.0}
flutter: ⚠️ WARNING [2025-06-24T01:43:39.270441] [PerformanceMonitoringService] High memory usage detected {"memory_mb":170.0}
flutter: ⚠️ WARNING [2025-06-24T01:43:54.270495] [PerformanceMonitoringService] High memory usage detected {"memory_mb":170.0}
flutter: ⚠️ WARNING [2025-06-24T01:44:09.271669] [PerformanceMonitoringService] High memory usage detected {"memory_mb":171.0}
flutter: ⚠️ WARNING [2025-06-24T01:44:24.258555] [PerformanceMonitoringService] High memory usage detected {"memory_mb":158.0}
flutter: ⚠️ WARNING [2025-06-24T01:44:44.241745] [PerformanceMonitoringService] High memory usage detected {"memory_mb":191.0}
flutter: ⚠️ WARNING [2025-06-24T01:44:59.236689] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: ⚠️ WARNING [2025-06-24T01:45:14.237050] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: ⚠️ WARNING [2025-06-24T01:45:29.234638] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: ⚠️ WARNING [2025-06-24T01:45:44.234463] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: ⚠️ WARNING [2025-06-24T01:45:59.235098] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: ⚠️ WARNING [2025-06-24T01:46:14.233365] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: ⚠️ WARNING [2025-06-24T01:46:29.233638] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: ⚠️ WARNING [2025-06-24T01:46:44.234358] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: ⚠️ WARNING [2025-06-24T01:46:59.235104] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: ⚠️ WARNING [2025-06-24T01:47:14.233862] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: ⚠️ WARNING [2025-06-24T01:47:29.234823] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: ⚠️ WARNING [2025-06-24T01:47:44.233404] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: ⚠️ WARNING [2025-06-24T01:47:59.233376] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 🐛 DEBUG [2025-06-24T01:48:00.088844] [PerformanceMonitoringService] Slow frame detected {"duration_ms":833}
flutter: 🐛 DEBUG [2025-06-24T01:48:00.205115] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-06-24T01:48:00.338523] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: 🐛 DEBUG [2025-06-24T01:48:00.421876] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:48:00.555202] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-06-24T01:48:00.788775] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:48:01.872193] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: 🐛 DEBUG [2025-06-24T01:48:02.105142] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-06-24T01:48:02.438840] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-06-24T01:48:02.605225] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-06-24T01:48:02.789437] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T01:48:03.171983] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: ⚠️ WARNING [2025-06-24T01:48:14.234544] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0