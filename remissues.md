[  +27 ms] executing: xcrun simctl launch 9DB20354-1C92-4494-AC81-A517DB5A8DEF com.example.cultureConnect
--enable-dart-profiling --enable-checked-mode --verify-entry-points
[+1334 ms] com.example.cultureConnect: 24558
[        ] Waiting for VM Service port to be available...
[+4583 ms] no valid “aps-environment” entitlement string found for application
[  +21 ms] VM Service URL on device: http://127.0.0.1:64630/DUSFjupURk4=/
[  +35 ms] Caching compiled dill
[ +485 ms] Connecting to service protocol: http://127.0.0.1:64630/DUSFjupURk4=/
[ +414 ms] Launching a Dart Developer Service (DDS) instance at http://127.0.0.1:0, connecting to VM service at
http://127.0.0.1:64630/DUSFjupURk4=/.
[+1396 ms] Successfully connected to service protocol: http://127.0.0.1:64630/DUSFjupURk4=/
[  +47 ms] DevFS: Creating new filesystem on the device (null)
[  +40 ms] DevFS: Created new filesystem on the device
(file:///Users/<USER>/Library/Developer/CoreSimulator/Devices/9DB20354-1C92-4494-AC81-A517DB5A8DEF/data/Containers/Data/Applica
tion/071C09A2-708E-4D69-9483-35A2DE29D326/tmp/culture_connectm5Alnj/culture_connect/)
[   +5 ms] Updating assets
[+1013 ms] Manifest contained wildcard assets. Inserting missing file into build graph to force rerun. for more information
see #56466.
[  +10 ms] Syncing files to device iPhone15ProMax_iOS17...
[   +5 ms] Compiling dart to kernel with 0 updated files
[   +2 ms] Processing bundle.
[   +4 ms] <- recompile package:culture_connect/main.dart 215fcd90-8dcb-48d8-93ed-a05981c03d9e
[   +1 ms] <- 215fcd90-8dcb-48d8-93ed-a05981c03d9e
[   +7 ms] Bundle processing done.
[  +37 ms] flutter: 🚀 Starting app initialization
[   +1 ms] flutter: ✅ SharedPreferences initialized in 67ms
[        ] flutter: ❌ Error preloading asset assets/images/splash.png: Unable to load asset: "assets/images/splash.png".
[   +3 ms] flutter: The asset does not exist or has empty data.
[        ] flutter: ✅ Preloaded asset: assets/animations/splash_animation.json (3741 bytes)
[        ] flutter: ✅ Critical assets preloaded in 145ms
[        ] flutter: ✅ Firebase core initialized in 367ms
[        ] flutter: ✅ App initialization completed in 394ms
[        ] flutter: ℹ️ INFO [2025-06-24T08:16:22.585243] [LoggingService] Logging service initialized successfully
[        ] flutter: ℹ️ INFO [2025-06-24T08:16:22.592429] [LoggingService] Device info: {name: iPhone15ProMax_iOS17, model:
iPhone, systemName: iOS, systemVersion: 17.2, platform: ios}
[        ] flutter: ℹ️ INFO [2025-06-24T08:16:22.593735] [LoggingService] App info: Culture Connect 1.0.0+1
[        ] flutter: ✅ Preloaded asset: assets/images/onboarding_1.png (0 bytes)
[        ] flutter: ✅ Firebase full features initialized in 131ms
[        ] flutter: ℹ️ INFO [2025-06-24T08:16:22.698465] [ErrorHandlingService] Error handling service initialized
[        ] flutter: ✅ Preloaded asset: assets/images/onboarding_2.png (0 bytes)
[        ] flutter: ✅ Preloaded asset: assets/images/onboarding_3.png (0 bytes)
[        ] flutter: ✅ Non-critical assets preloaded in 141ms
[        ] flutter: ℹ️ INFO [2025-06-24T08:16:22.721480] [CrashReportingService] Crash reporting service initialized
[        ] flutter: ℹ️ INFO [2025-06-24T08:16:22.744752] [AnalyticsService] Analytics service initialized
[        ] flutter: 🐛 DEBUG [2025-06-24T08:16:22.752152] [AnalyticsService] Event: app_session_begin
{"category":"engagement","parameters":{"timestamp":"2025-06-24T08:16:22.734666"}}
[        ] flutter: ℹ️ INFO [2025-06-24T08:16:22.757858] [PerformanceMonitoringService] Performance monitoring service
initialized
[   +1 ms] flutter: ℹ️ INFO [2025-06-24T08:16:22.758472] [App] All services initialized successfully
[        ] flutter: ⚠️ WARNING [2025-06-24T08:16:22.770862] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":157.0}
[+4193 ms] flutter: 🐛 DEBUG [2025-06-24T08:16:27.229405] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":257}
[  +51 ms] flutter: 🐛 DEBUG [2025-06-24T08:16:27.280317] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[ +130 ms] flutter: 🐛 DEBUG [2025-06-24T08:16:27.413735] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":101}
[ +283 ms] flutter: 🐛 DEBUG [2025-06-24T08:16:27.697960] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":116}
[  +82 ms] flutter: 🐛 DEBUG [2025-06-24T08:16:27.780314] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":34}
[  +94 ms] flutter: 🐛 DEBUG [2025-06-24T08:16:27.874677] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":93}
[  +24 ms] flutter: 🐛 DEBUG [2025-06-24T08:16:27.896292] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":21}
[ +237 ms] flutter: 🐛 DEBUG [2025-06-24T08:16:28.135726] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":105}
[  +45 ms] flutter: 🐛 DEBUG [2025-06-24T08:16:28.179573] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":44}
[  +35 ms] flutter: 🐛 DEBUG [2025-06-24T08:16:28.214045] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[+1309 ms] Updating files.
[        ] Pending asset builds completed. Writing dirty entries.
[        ] DevFS: Sync finished
[   +1 ms] Syncing files to device iPhone15ProMax_iOS17... (completed in 6.6s)
[   +2 ms] Synced 0.0MB.
[   +4 ms] <- accept
[   +7 ms] Connected to _flutterView/0x10e027a20.
[  +19 ms] Flutter run key commands.
[   +6 ms] r Hot reload. 🔥🔥🔥
[   +1 ms] R Hot restart.
[        ] h List all available interactive commands.
[        ] d Detach (terminate "flutter run" but leave application running).
[        ] c Clear the screen
[        ] q Quit (terminate the application on the device).
[   +3 ms] A Dart VM Service on iPhone15ProMax_iOS17 is available at: http://127.0.0.1:64639/DqQHRI2YIS8=/
[   +6 ms] The Flutter DevTools debugger and profiler on iPhone15ProMax_iOS17 is available at:
           http://127.0.0.1:9100?uri=http://127.0.0.1:64639/DqQHRI2YIS8=/
[+2978 ms] flutter: 🐛 DEBUG [2025-06-24T08:16:32.562715] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[+5193 ms] flutter: ⚠️ WARNING [2025-06-24T08:16:37.757405] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":157.0}
[+15000 ms] flutter: ⚠️ WARNING [2025-06-24T08:16:52.757564] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":157.0}
[+7384 ms] flutter: 🐛 DEBUG [2025-06-24T08:17:00.129945] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[+7618 ms] flutter: ⚠️ WARNING [2025-06-24T08:17:07.759037] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":158.0}
[+1658 ms] flutter: 🐛 DEBUG [2025-06-24T08:17:09.413520] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[+13341 ms] flutter: ⚠️ WARNING [2025-06-24T08:17:22.759488] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":159.0}
[+14999 ms] flutter: ⚠️ WARNING [2025-06-24T08:17:37.757880] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":157.0}
[+14997 ms] flutter: ⚠️ WARNING [2025-06-24T08:17:52.757792] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":157.0}
[+15001 ms] flutter: ⚠️ WARNING [2025-06-24T08:18:07.759233] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":159.0}
[+1301 ms] flutter: 🐛 DEBUG [2025-06-24T08:18:09.013076] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":66}
[+13698 ms] flutter: ⚠️ WARNING [2025-06-24T08:18:22.759136] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":159.0}
[+14999 ms] flutter: ⚠️ WARNING [2025-06-24T08:18:37.758186] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":158.0}
[+14999 ms] flutter: ⚠️ WARNING [2025-06-24T08:18:52.758348] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":158.0}
[+14999 ms] flutter: ⚠️ WARNING [2025-06-24T08:19:07.758035] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":157.0}
[+14999 ms] flutter: ⚠️ WARNING [2025-06-24T08:19:22.757305] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":157.0}
[+15003 ms] flutter: ⚠️ WARNING [2025-06-24T08:19:37.757931] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":157.0}
[+3629 ms] flutter: 🐛 DEBUG [2025-06-24T08:19:41.345362] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":50}
[+3541 ms] flutter: 🐛 DEBUG [2025-06-24T08:19:44.929072] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[ +769 ms] flutter: 🐛 DEBUG [2025-06-24T08:19:45.495504] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":50}
[  +22 ms] flutter: 🐛 DEBUG [2025-06-24T08:19:45.529077] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[+12034 ms] flutter: ⚠️ WARNING [2025-06-24T08:19:57.703071] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":152.0}
[+14998 ms] flutter: ⚠️ WARNING [2025-06-24T08:20:12.702283] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":152.0}
[+15002 ms] flutter: ⚠️ WARNING [2025-06-24T08:20:27.703384] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":153.0}
[ +963 ms] flutter: 🐛 DEBUG [2025-06-24T08:20:28.667533] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":43}
[  +23 ms] flutter: 🐛 DEBUG [2025-06-24T08:20:28.691735] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":23}
[+16111 ms] flutter: ⚠️ WARNING [2025-06-24T08:20:44.803909] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":153.0}
[ +974 ms] flutter: 🐛 DEBUG [2025-06-24T08:20:45.772388] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":16429}
[+1933 ms] flutter: ⚠️ WARNING [2025-06-24T08:20:48.748886] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":198.0}
[+14992 ms] flutter: ⚠️ WARNING [2025-06-24T08:21:03.749596] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":199.0}
[+14998 ms] flutter: ⚠️ WARNING [2025-06-24T08:21:18.748534] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":198.0}
[+263038 ms] flutter: 🐛 DEBUG [2025-06-24T08:25:41.782108] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":263349}
[+16960 ms] flutter: ⚠️ WARNING [2025-06-24T08:25:58.752857] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":152.0}
[+15001 ms] flutter: ⚠️ WARNING [2025-06-24T08:26:13.755520] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":155.0}
[+263025 ms] flutter: 🐛 DEBUG [2025-06-24T08:30:36.781019] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":263354}
[+6980 ms] flutter: ⚠️ WARNING [2025-06-24T08:30:43.619748] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":169.0}
[+14994 ms] flutter: ⚠️ WARNING [2025-06-24T08:30:58.619060] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":168.0}
[+272137 ms] flutter: 🐛 DEBUG [2025-06-24T08:35:30.756839] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":262049}
[+12861 ms] flutter: ⚠️ WARNING [2025-06-24T08:35:43.768232] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":168.0}
[+15000 ms] flutter: ⚠️ WARNING [2025-06-24T08:35:58.767881] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":167.0}
[+266024 ms] flutter: 🐛 DEBUG [2025-06-24T08:40:24.791452] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":262247}
[+8982 ms] flutter: ⚠️ WARNING [2025-06-24T08:40:33.712212] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":161.0}
[+14999 ms] flutter: ⚠️ WARNING [2025-06-24T08:40:48.711928] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":161.0}
[+269992 ms] flutter: ⚠️ WARNING [2025-06-24T08:45:18.710628] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":160.0}
[+1051 ms] flutter: 🐛 DEBUG [2025-06-24T08:45:19.762044] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":262931}
[+8956 ms] flutter: ⚠️ WARNING [2025-06-24T08:45:29.502236] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":151.0}
[+14997 ms] flutter: ⚠️ WARNING [2025-06-24T08:45:44.502892] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":152.0}
[+268527 ms] flutter: ⚠️ WARNING [2025-06-24T08:50:13.033008] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":182.0}
[+1739 ms] flutter: 🐛 DEBUG [2025-06-24T08:50:14.843349] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":262088}
[+9738 ms] flutter: ⚠️ WARNING [2025-06-24T08:50:24.576329] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":176.0}
[+14999 ms] flutter: ⚠️ WARNING [2025-06-24T08:50:39.580439] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":180.0}
[+268643 ms] flutter: ⚠️ WARNING [2025-06-24T08:55:08.234763] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":184.0}
[ +532 ms] flutter: 🐛 DEBUG [2025-06-24T08:55:08.766881] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":262230}
[+5816 ms] flutter: ⚠️ WARNING [2025-06-24T08:55:14.961952] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":161.0}
[+12080 ms] flutter: 🐛 DEBUG [2025-06-24T08:55:26.996515] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[+1484 ms] flutter: 🐛 DEBUG [2025-06-24T08:55:28.513103] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[+1438 ms] flutter: ⚠️ WARNING [2025-06-24T08:55:29.960126] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":159.0}
[+272812 ms] flutter: 🐛 DEBUG [2025-06-24T09:00:02.776898] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261958}
[+2182 ms] flutter: ⚠️ WARNING [2025-06-24T09:00:05.224805] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":174.0}
[+15005 ms] flutter: ⚠️ WARNING [2025-06-24T09:00:20.224158] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":173.0}
[+15005 ms] flutter: ⚠️ WARNING [2025-06-24T09:00:35.229466] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":179.0}
[+261551 ms] flutter: 🐛 DEBUG [2025-06-24T09:04:56.782943] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261644}
[+3436 ms] flutter: ⚠️ WARNING [2025-06-24T09:05:00.889476] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":189.0}
[+15005 ms] flutter: ⚠️ WARNING [2025-06-24T09:05:15.891217] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":191.0}
[+14999 ms] flutter: ⚠️ WARNING [2025-06-24T09:05:30.891788] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":191.0}
[+333859 ms] flutter: ⚠️ WARNING [2025-06-24T09:11:04.754868] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":154.0}
[  +18 ms] flutter: 🐛 DEBUG [2025-06-24T09:11:04.772658] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":334820}
[+11126 ms] flutter: ⚠️ WARNING [2025-06-24T09:11:15.890821] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":190.0}
[+9993 ms] flutter: ⚠️ WARNING [2025-06-24T09:11:26.051984] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":151.0}
[+273719 ms] flutter: 🐛 DEBUG [2025-06-24T09:15:59.775025] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":262077}
[+11312 ms] flutter: ⚠️ WARNING [2025-06-24T09:16:11.362983] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":162.0}
[+14969 ms] flutter: ⚠️ WARNING [2025-06-24T09:16:26.362497] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":162.0}
[+910418 ms] flutter: 🐛 DEBUG [2025-06-24T09:31:36.782182] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":905011}
[+14582 ms] flutter: ⚠️ WARNING [2025-06-24T09:31:51.748176] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":198.0}
[+15001 ms] flutter: ⚠️ WARNING [2025-06-24T09:32:06.748440] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":198.0}
[+1736 ms] flutter: 🐛 DEBUG [2025-06-24T09:32:08.397045] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[ +184 ms] flutter: 🐛 DEBUG [2025-06-24T09:32:08.652077] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":38}
[  +21 ms] flutter: 🐛 DEBUG [2025-06-24T09:32:08.680629] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":28}
[+599092 ms] flutter: 🐛 DEBUG [2025-06-24T09:42:07.787937] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":598971}
[+13965 ms] flutter: ⚠️ WARNING [2025-06-24T09:42:22.326074] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":175.0}
[+15000 ms] flutter: ⚠️ WARNING [2025-06-24T09:42:37.326034] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":175.0}
[+398264 ms] flutter: ⚠️ WARNING [2025-06-24T09:49:15.596403] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":196.0}
[+1181 ms] flutter: 🐛 DEBUG [2025-06-24T09:49:16.777612] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":396780}
[+5548 ms] flutter: ⚠️ WARNING [2025-06-24T09:49:22.759061] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":158.0}
[+15005 ms] flutter: ⚠️ WARNING [2025-06-24T09:49:37.759919] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":159.0}
[+273014 ms] flutter: 🐛 DEBUG [2025-06-24T09:54:10.779410] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261451}
[+31984 ms] flutter: ⚠️ WARNING [2025-06-24T09:54:43.903297] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":153.0}
[+261876 ms] flutter: 🐛 DEBUG [2025-06-24T09:59:05.782073] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":262210}
[+3117 ms] flutter: ⚠️ WARNING [2025-06-24T09:59:08.952666] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":152.0}
[+15004 ms] flutter: ⚠️ WARNING [2025-06-24T09:59:23.952357] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":152.0}
[+274060 ms] flutter: ⚠️ WARNING [2025-06-24T10:03:58.012900] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":162.0}
[+1748 ms] flutter: 🐛 DEBUG [2025-06-24T10:03:59.765580] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":262266}
[+4186 ms] flutter: ⚠️ WARNING [2025-06-24T10:04:03.881477] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":181.0}
[+15016 ms] flutter: ⚠️ WARNING [2025-06-24T10:04:18.883292] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":183.0}
[+273590 ms] flutter: ⚠️ WARNING [2025-06-24T10:08:52.488354] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":188.0}
[+1293 ms] flutter: 🐛 DEBUG [2025-06-24T10:08:53.781067] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261984}
[+10104 ms] flutter: ⚠️ WARNING [2025-06-24T10:09:04.325505] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":175.0}
[+15019 ms] flutter: ⚠️ WARNING [2025-06-24T10:09:19.325420] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":175.0}
[+268409 ms] flutter: 🐛 DEBUG [2025-06-24T10:13:48.369844] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261435}
[+1567 ms] flutter: ⚠️ WARNING [2025-06-24T10:13:49.937509] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":187.0}
[+25006 ms] flutter: ⚠️ WARNING [2025-06-24T10:14:14.956548] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":156.0}
[+265798 ms] flutter: ⚠️ WARNING [2025-06-24T10:18:40.760872] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":160.0}
[   +6 ms] flutter: 🐛 DEBUG [2025-06-24T10:18:40.766661] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":260694}
[+14192 ms] flutter: ⚠️ WARNING [2025-06-24T10:18:56.671174] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":170.0}
[+1662 ms] flutter: 🐛 DEBUG [2025-06-24T10:18:58.284053] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[+13335 ms] flutter: ⚠️ WARNING [2025-06-24T10:19:11.671306] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":171.0}
[+266109 ms] flutter: 🐛 DEBUG [2025-06-24T10:23:37.781835] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":263633}
[+3886 ms] flutter: ⚠️ WARNING [2025-06-24T10:23:41.514222] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":164.0}
[+15005 ms] flutter: ⚠️ WARNING [2025-06-24T10:23:56.515846] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":165.0}
[+275257 ms] flutter: 🐛 DEBUG [2025-06-24T10:28:31.776250] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":262483}
[+4736 ms] flutter: ⚠️ WARNING [2025-06-24T10:28:36.252059] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":151.0}
[+15006 ms] flutter: ⚠️ WARNING [2025-06-24T10:28:51.255305] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":155.0}
[+273599 ms] flutter: ⚠️ WARNING [2025-06-24T10:33:24.857815] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":157.0}
[ +923 ms] flutter: 🐛 DEBUG [2025-06-24T10:33:25.780286] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":262148}
[+5472 ms] flutter: ⚠️ WARNING [2025-06-24T10:33:31.494856] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":194.0}
[+15004 ms] flutter: ⚠️ WARNING [2025-06-24T10:33:46.495241] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":195.0}
[+273107 ms] flutter: ⚠️ WARNING [2025-06-24T10:38:19.607312] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":157.0}
[ +153 ms] flutter: 🐛 DEBUG [2025-06-24T10:38:19.759767] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261621}
[+6734 ms] flutter: ⚠️ WARNING [2025-06-24T10:38:27.147632] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":197.0}
[+15010 ms] flutter: ⚠️ WARNING [2025-06-24T10:38:42.148570] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":198.0}
[+271603 ms] flutter: ⚠️ WARNING [2025-06-24T10:43:13.762267] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":162.0}
[   +3 ms] flutter: 🐛 DEBUG [2025-06-24T10:43:13.765963] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261669}
[+8385 ms] flutter: ⚠️ WARNING [2025-06-24T10:43:22.794048] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":193.0}
[+15005 ms] flutter: ⚠️ WARNING [2025-06-24T10:43:37.793355] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":193.0}
[+269966 ms] flutter: 🐛 DEBUG [2025-06-24T10:48:07.768802] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261590}
[  +25 ms] flutter: ⚠️ WARNING [2025-06-24T10:48:07.794349] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":194.0}
[+10002 ms] flutter: ⚠️ WARNING [2025-06-24T10:48:18.408689] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":158.0}
[+15002 ms] flutter: ⚠️ WARNING [2025-06-24T10:48:33.408948] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":158.0}
[+269350 ms] flutter: 🐛 DEBUG [2025-06-24T10:53:02.764592] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":262281}
[+5642 ms] flutter: ⚠️ WARNING [2025-06-24T10:53:08.666975] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":166.0}
[+6012 ms] flutter: 🐛 DEBUG [2025-06-24T10:53:14.673839] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[+6128 ms] flutter: 🐛 DEBUG [2025-06-24T10:53:20.725077] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[   +6 ms] flutter: 🐛 DEBUG [2025-06-24T10:53:20.790998] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":66}
[+2862 ms] flutter: ⚠️ WARNING [2025-06-24T10:53:23.667430] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":167.0}
[+273111 ms] flutter: 🐛 DEBUG [2025-06-24T10:57:56.787785] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261660}
[+11886 ms] flutter: ⚠️ WARNING [2025-06-24T10:58:09.424183] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":173.0}
[+14997 ms] flutter: ⚠️ WARNING [2025-06-24T10:58:24.424815] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":174.0}
[+264884 ms] flutter: ⚠️ WARNING [2025-06-24T11:02:49.312656] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":162.0}
[ +176 ms] flutter: ⚠️ WARNING [2025-06-24T11:02:49.489078] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":188.0}
[+1271 ms] flutter: 🐛 DEBUG [2025-06-24T11:02:50.760251] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261553}
[+3663 ms] flutter: ⚠️ WARNING [2025-06-24T11:02:55.170627] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":170.0}
[+15003 ms] flutter: ⚠️ WARNING [2025-06-24T11:03:10.171007] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":170.0}
[+274595 ms] flutter: 🐛 DEBUG [2025-06-24T11:07:44.770785] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261596}
[+10407 ms] flutter: ⚠️ WARNING [2025-06-24T11:07:55.907515] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":157.0}
[+14996 ms] flutter: ⚠️ WARNING [2025-06-24T11:08:10.907770] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":157.0}
[+268653 ms] flutter: ⚠️ WARNING [2025-06-24T11:12:39.564452] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":164.0}
[ +195 ms] flutter: 🐛 DEBUG [2025-06-24T11:12:39.758271] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":262150}
[+11157 ms] flutter: ⚠️ WARNING [2025-06-24T11:12:50.959235] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":159.0}
[+14995 ms] flutter: ⚠️ WARNING [2025-06-24T11:13:05.959031] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":158.0}
[+267804 ms] flutter: 🐛 DEBUG [2025-06-24T11:17:33.767867] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261874}
[+2189 ms] flutter: ⚠️ WARNING [2025-06-24T11:17:36.413743] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":163.0}
[+15004 ms] flutter: ⚠️ WARNING [2025-06-24T11:17:51.414049] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":163.0}
[+15001 ms] flutter: ⚠️ WARNING [2025-06-24T11:18:06.416535] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":166.0}
[+260337 ms] flutter: 🐛 DEBUG [2025-06-24T11:22:28.160642] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":260866}
[   +1 ms] flutter: ⚠️ WARNING [2025-06-24T11:22:28.162425] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":162.0}
[+9659 ms] flutter: ⚠️ WARNING [2025-06-24T11:22:38.068022] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":167.0}
[+15000 ms] flutter: ⚠️ WARNING [2025-06-24T11:22:53.068159] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":167.0}
[+267900 ms] flutter: ⚠️ WARNING [2025-06-24T11:27:20.971434] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":171.0}
[ +806 ms] flutter: 🐛 DEBUG [2025-06-24T11:27:21.778620] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261445}
[+1289 ms] flutter: ⚠️ WARNING [2025-06-24T11:27:23.812316] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":162.0}
[+15003 ms] flutter: ⚠️ WARNING [2025-06-24T11:27:38.811687] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":161.0}
[+15001 ms] flutter: ⚠️ WARNING [2025-06-24T11:27:53.813158] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":162.0}
[+262959 ms] flutter: ⚠️ WARNING [2025-06-24T11:32:16.776851] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":176.0}
[  +14 ms] flutter: 🐛 DEBUG [2025-06-24T11:32:16.791902] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":262175}
[+2018 ms] flutter: ⚠️ WARNING [2025-06-24T11:32:19.172987] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":172.0}
[+15005 ms] flutter: ⚠️ WARNING [2025-06-24T11:32:34.174325] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":174.0}
[+15000 ms] flutter: ⚠️ WARNING [2025-06-24T11:32:49.174916] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":174.0}
[+261580 ms] flutter: ⚠️ WARNING [2025-06-24T11:37:10.758612] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":158.0}
[   +2 ms] flutter: 🐛 DEBUG [2025-06-24T11:37:10.760908] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261957}
[+8137 ms] flutter: 🐛 DEBUG [2025-06-24T11:37:19.128816] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":50}
[+5280 ms] flutter: ⚠️ WARNING [2025-06-24T11:37:24.423751] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":173.0}
[+14999 ms] flutter: ⚠️ WARNING [2025-06-24T11:37:39.426375] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":176.0}
[+265347 ms] flutter: 🐛 DEBUG [2025-06-24T11:42:04.776634] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":262099}
[+4646 ms] flutter: ⚠️ WARNING [2025-06-24T11:42:09.431440] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":181.0}
[+9676 ms] flutter: 🐛 DEBUG [2025-06-24T11:42:19.119260] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[+15329 ms] flutter: ⚠️ WARNING [2025-06-24T11:42:34.463082] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":162.0}
[+264302 ms] flutter: 🐛 DEBUG [2025-06-24T11:46:58.771630] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":262304}
[+10701 ms] flutter: ⚠️ WARNING [2025-06-24T11:47:09.599996] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":199.0}
[+283155 ms] flutter: ⚠️ WARNING [2025-06-24T11:51:52.763626] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":163.0}
[  +14 ms] flutter: 🐛 DEBUG [2025-06-24T11:51:52.778377] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261802}
[+6822 ms] flutter: ⚠️ WARNING [2025-06-24T11:52:00.137267] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":187.0}
[+15001 ms] flutter: ⚠️ WARNING [2025-06-24T11:52:15.136239] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":185.0}
[+271622 ms] flutter: ⚠️ WARNING [2025-06-24T11:56:46.761245] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":161.0}
[   +4 ms] flutter: 🐛 DEBUG [2025-06-24T11:56:46.766431] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261787}
[+3368 ms] flutter: ⚠️ WARNING [2025-06-24T11:56:50.661230] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":161.0}
[+5025 ms] flutter: 🐛 DEBUG [2025-06-24T11:56:55.642546] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[+9993 ms] flutter: ⚠️ WARNING [2025-06-24T11:57:05.661657] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":161.0}
[+274104 ms] flutter: 🐛 DEBUG [2025-06-24T12:01:39.779431] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":260830}
[ +881 ms] flutter: ⚠️ WARNING [2025-06-24T12:01:41.961524] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":161.0}
[+15000 ms] flutter: ⚠️ WARNING [2025-06-24T12:01:56.961440] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":161.0}
[+15000 ms] flutter: ⚠️ WARNING [2025-06-24T12:02:11.961799] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":161.0}
[+262804 ms] flutter: ⚠️ WARNING [2025-06-24T12:06:34.769705] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":169.0}
[  +18 ms] flutter: 🐛 DEBUG [2025-06-24T12:06:34.787949] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261592}
[+7175 ms] flutter: ⚠️ WARNING [2025-06-24T12:06:42.886293] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":186.0}
[+15002 ms] flutter: ⚠️ WARNING [2025-06-24T12:06:57.884533] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":184.0}
[+271894 ms] flutter: 🐛 DEBUG [2025-06-24T12:11:29.783298] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":262407}
[+3100 ms] flutter: ⚠️ WARNING [2025-06-24T12:11:32.806664] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":156.0}
[+8152 ms] flutter: 🐛 DEBUG [2025-06-24T12:11:40.918650] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[+6853 ms] flutter: ⚠️ WARNING [2025-06-24T12:11:47.807339] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":157.0}
[+4279 ms] flutter: 🐛 DEBUG [2025-06-24T12:11:52.085692] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[+269935 ms] flutter: ⚠️ WARNING [2025-06-24T12:16:22.026721] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":176.0}
[+1733 ms] flutter: 🐛 DEBUG [2025-06-24T12:16:23.760123] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261942}
[+4048 ms] flutter: ⚠️ WARNING [2025-06-24T12:16:28.019497] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":169.0}
[+15003 ms] flutter: ⚠️ WARNING [2025-06-24T12:16:43.017618] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":167.0}
[+303582 ms] flutter: ⚠️ WARNING [2025-06-24T12:21:46.606340] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":156.0}
[+1164 ms] flutter: 🐛 DEBUG [2025-06-24T12:21:47.770417] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":292115}
[+10252 ms] flutter: ⚠️ WARNING [2025-06-24T12:21:58.339684] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":189.0}
[+14998 ms] flutter: ⚠️ WARNING [2025-06-24T12:22:13.340080] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":189.0}
[+268427 ms] flutter: 🐛 DEBUG [2025-06-24T12:26:42.475795] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261562}
[+6568 ms] flutter: ⚠️ WARNING [2025-06-24T12:26:49.172771] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":172.0}
[+6743 ms] flutter: 🐛 DEBUG [2025-06-24T12:26:55.888864] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[+8259 ms] flutter: ⚠️ WARNING [2025-06-24T12:27:04.172021] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":171.0}
[+271599 ms] flutter: 🐛 DEBUG [2025-06-24T12:31:35.772402] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261502}
[+1865 ms] flutter: 🐛 DEBUG [2025-06-24T12:31:38.408745] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[+6535 ms] flutter: ⚠️ WARNING [2025-06-24T12:31:44.975395] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":175.0}
[+15005 ms] flutter: ⚠️ WARNING [2025-06-24T12:31:59.978180] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":177.0}
[+268033 ms] flutter: ⚠️ WARNING [2025-06-24T12:36:28.013300] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":163.0}
[+1756 ms] flutter: 🐛 DEBUG [2025-06-24T12:36:29.775229] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261550}
[ +201 ms] flutter: ⚠️ WARNING [2025-06-24T12:36:29.976202] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":176.0}
[+10008 ms] flutter: ⚠️ WARNING [2025-06-24T12:36:40.910488] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":160.0}
[+12047 ms] flutter: 🐛 DEBUG [2025-06-24T12:36:52.949072] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":42}
[  +16 ms] flutter: 🐛 DEBUG [2025-06-24T12:36:52.975194] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":24}
[+2934 ms] flutter: ⚠️ WARNING [2025-06-24T12:36:55.908684] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":158.0}
[+267849 ms] flutter: 🐛 DEBUG [2025-06-24T12:41:23.764879] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":261407}
[+12146 ms] flutter: ⚠️ WARNING [2025-06-24T12:41:36.597002] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":196.0}
[+14999 ms] flutter: ⚠️ WARNING [2025-06-24T12:41:51.595515] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":195.0}
[+163974 ms] flutter: ⚠️ WARNING [2025-06-24T12:44:35.548278] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":198.0}
[   +7 ms] flutter: 🐛 DEBUG [2025-06-24T12:44:35.555214] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":159000}
[  +90 ms] flutter: 🐛 DEBUG [2025-06-24T12:44:35.670431] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":50}
[ +380 ms] flutter: 🐛 DEBUG [2025-06-24T12:44:36.054549] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":199}
[ +133 ms] flutter: 🐛 DEBUG [2025-06-24T12:44:36.187108] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":99}
[+1251 ms] flutter: 🐛 DEBUG [2025-06-24T12:44:37.958119] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":983}
[ +148 ms] flutter: 🐛 DEBUG [2025-06-24T12:44:38.108133] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":116}
[ +133 ms] flutter: 🐛 DEBUG [2025-06-24T12:44:38.241665] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":100}
[+1516 ms] flutter: 🐛 DEBUG [2025-06-24T12:44:39.758331] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":83}
[ +199 ms] flutter: 🐛 DEBUG [2025-06-24T12:44:39.958185] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":83}
[+1672 ms] flutter: 🐛 DEBUG [2025-06-24T12:44:41.591458] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[ +486 ms] flutter: ⚠️ WARNING [2025-06-24T12:44:42.116967] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":166.0}
[+14999 ms] flutter: ⚠️ WARNING [2025-06-24T12:44:57.116790] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":166.0}
[+14999 ms] flutter: ⚠️ WARNING [2025-06-24T12:45:12.115723] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":165.0}
[+15001 ms] flutter: ⚠️ WARNING [2025-06-24T12:45:27.117235] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":167.0}
[+1991 ms] flutter: 🐛 DEBUG [2025-06-24T12:45:29.108155] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":83}
[ +238 ms] flutter: 🐛 DEBUG [2025-06-24T12:45:29.258287] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[   +1 ms] flutter: 🐛 DEBUG [2025-06-24T12:45:29.291505] [PerformanceMonitoringService] Slow frame detected
{"duration_ms":33}
[+12766 ms] flutter: ⚠️ WARNING [2025-06-24T12:45:42.115629] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":165.0}
[+15000 ms] flutter: ⚠️ WARNING [2025-06-24T12:45:57.116385] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":166.0}
[+15000 ms] flutter: ⚠️ WARNING [2025-06-24T12:46:12.116427] [PerformanceMonitoringService] High memory usage detected
{"memory_mb":166.0}
