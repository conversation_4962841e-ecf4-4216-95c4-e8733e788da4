flutter: #30     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #31     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #32     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #33     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #34     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #35     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #36     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #37     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #38     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #39     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #40     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #41     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #44     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #45     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #46     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #47     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #48     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #49     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #50     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #51     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #52     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #53     RenderOffstage.performLayout (package:flutter/src/rendering/proxy_box.dart:3720:14)
flutter: #54     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #55     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #56     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #57     _RenderTheaterMixin.layoutChild (package:flutter/src/widgets/overlay.dart:1016:13)
flutter: #58     _RenderTheater.performLayout (package:flutter/src/widgets/overlay.dart:1328:9)
flutter: #59     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #60     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #61     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #62     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #63     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #64     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #65     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #66     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #67     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #68     _invoke (dart:ui/hooks.dart:312:13)
flutter: #69     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #70     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-06-24T19:11:36.513780] [FlutterError] 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: _RenderSingleChildViewport#d7058 relayoutBoundary=up9 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: _RenderSingleChildViewport#d7058 relayoutBoundary=up9 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      RenderBox.size (package:flutter/src/rendering/box.dart:2176:12)
flutter: #3      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:65)
flutter: #4      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #5      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #6      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #7      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #8      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #9      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #10     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #11     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #12     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #13     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #14     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #15     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #16     RenderFlex._computeSizes (package:flutter/src/rendering/flex.dart:1079:71)
flutter: #17     RenderFlex.performLayout (package:flutter/src/rendering/flex.dart:1121:32)
flutter: #18     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #19     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #20     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #21     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #22     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #23     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #24     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #25     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #26     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #27     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #28     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #29     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #30     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #31     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #32     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #33     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #34     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #35     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #36     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #37     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #38     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #39     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #40     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #41     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #44     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #45     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #46     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #47     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #48     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #49     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #50     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #51     RenderOffstage.performLayout (package:flutter/src/rendering/proxy_box.dart:3720:14)
flutter: #52     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #53     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #54     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #55     _RenderTheaterMixin.layoutChild (package:flutter/src/widgets/overlay.dart:1016:13)
flutter: #56     _RenderTheater.performLayout (package:flutter/src/widgets/overlay.dart:1328:9)
flutter: #57     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #58     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #59     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #60     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #61     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #62     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #63     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #64     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #65     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #66     _invoke (dart:ui/hooks.dart:312:13)
flutter: #67     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #68     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-06-24T19:11:36.516227] [FlutterError] 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: RenderIgnorePointer#7d4a5 relayoutBoundary=up8 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: RenderIgnorePointer#7d4a5 relayoutBoundary=up8 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      RenderBox.size (package:flutter/src/rendering/box.dart:2176:12)
flutter: #3      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:65)
flutter: #4      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #5      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #6      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #7      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #8      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #9      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #10     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #11     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #12     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #13     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #14     RenderFlex._computeSizes (package:flutter/src/rendering/flex.dart:1079:71)
flutter: #15     RenderFlex.performLayout (package:flutter/src/rendering/flex.dart:1121:32)
flutter: #16     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #17     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #18     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #19     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #20     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #21     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #22     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #23     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #24     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #25     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #26     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #27     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #28     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #29     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #30     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #31     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #32     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #33     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #34     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #35     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #36     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #37     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #38     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #39     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #40     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #41     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #44     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #45     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #46     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #47     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #48     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #49     RenderOffstage.performLayout (package:flutter/src/rendering/proxy_box.dart:3720:14)
flutter: #50     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #51     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #52     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #53     _RenderTheaterMixin.layoutChild (package:flutter/src/widgets/overlay.dart:1016:13)
flutter: #54     _RenderTheater.performLayout (package:flutter/src/widgets/overlay.dart:1328:9)
flutter: #55     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #56     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #57     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #58     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #59     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #60     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #61     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #62     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #63     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #64     _invoke (dart:ui/hooks.dart:312:13)
flutter: #65     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #66     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-06-24T19:11:36.518594] [FlutterError] 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: RenderSemanticsAnnotations#46f43 relayoutBoundary=up7 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: RenderSemanticsAnnotations#46f43 relayoutBoundary=up7 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      RenderBox.size (package:flutter/src/rendering/box.dart:2176:12)
flutter: #3      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:65)
flutter: #4      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #5      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #6      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #7      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #8      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #9      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #10     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #11     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #12     RenderFlex._computeSizes (package:flutter/src/rendering/flex.dart:1079:71)
flutter: #13     RenderFlex.performLayout (package:flutter/src/rendering/flex.dart:1121:32)
flutter: #14     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #15     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #16     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #17     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #18     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #19     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #20     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #21     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #22     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #23     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #24     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #25     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #26     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #27     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #28     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #29     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #30     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #31     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #32     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #33     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #34     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #35     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #36     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #37     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #38     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #39     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #40     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #41     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #44     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #45     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #46     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #47     RenderOffstage.performLayout (package:flutter/src/rendering/proxy_box.dart:3720:14)
flutter: #48     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #49     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #50     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #51     _RenderTheaterMixin.layoutChild (package:flutter/src/widgets/overlay.dart:1016:13)
flutter: #52     _RenderTheater.performLayout (package:flutter/src/widgets/overlay.dart:1328:9)
flutter: #53     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #54     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #55     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #56     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #57     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #58     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #59     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #60     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #61     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #62     _invoke (dart:ui/hooks.dart:312:13)
flutter: #63     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #64     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-06-24T19:11:36.520704] [FlutterError] 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: RenderPointerListener#1f701 relayoutBoundary=up6 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: RenderPointerListener#1f701 relayoutBoundary=up6 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      RenderBox.size (package:flutter/src/rendering/box.dart:2176:12)
flutter: #3      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:65)
flutter: #4      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #5      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #6      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #7      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #8      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #9      ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #10     RenderFlex._computeSizes (package:flutter/src/rendering/flex.dart:1079:71)
flutter: #11     RenderFlex.performLayout (package:flutter/src/rendering/flex.dart:1121:32)
flutter: #12     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #13     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #14     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #15     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #16     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #17     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #18     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #19     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #20     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #21     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #22     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #23     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #24     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #25     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #26     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #27     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #28     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #29     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #30     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #31     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #32     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #33     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #34     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #35     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #36     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #37     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #38     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #39     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #40     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #41     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #44     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #45     RenderOffstage.performLayout (package:flutter/src/rendering/proxy_box.dart:3720:14)
flutter: #46     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #47     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #48     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #49     _RenderTheaterMixin.layoutChild (package:flutter/src/widgets/overlay.dart:1016:13)
flutter: #50     _RenderTheater.performLayout (package:flutter/src/widgets/overlay.dart:1328:9)
flutter: #51     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #52     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #53     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #54     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #55     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #56     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #57     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #58     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #59     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #60     _invoke (dart:ui/hooks.dart:312:13)
flutter: #61     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #62     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-06-24T19:11:36.522925] [FlutterError] 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: RenderSemanticsGestureHandler#8cbf9 relayoutBoundary=up5 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: RenderSemanticsGestureHandler#8cbf9 relayoutBoundary=up5 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      RenderBox.size (package:flutter/src/rendering/box.dart:2176:12)
flutter: #3      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:65)
flutter: #4      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #5      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #6      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #7      ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #8      RenderFlex._computeSizes (package:flutter/src/rendering/flex.dart:1079:71)
flutter: #9      RenderFlex.performLayout (package:flutter/src/rendering/flex.dart:1121:32)
flutter: #10     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #11     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #12     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #13     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #14     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #15     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #16     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #17     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #18     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #19     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #20     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #21     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #22     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #23     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #24     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #25     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #26     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #27     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #28     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #29     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #30     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #31     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #32     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #33     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #34     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #35     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #36     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #37     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #38     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #39     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #40     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #41     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderOffstage.performLayout (package:flutter/src/rendering/proxy_box.dart:3720:14)
flutter: #44     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #45     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #46     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #47     _RenderTheaterMixin.layoutChild (package:flutter/src/widgets/overlay.dart:1016:13)
flutter: #48     _RenderTheater.performLayout (package:flutter/src/widgets/overlay.dart:1328:9)
flutter: #49     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #50     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #51     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #52     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #53     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #54     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #55     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #56     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #57     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #58     _invoke (dart:ui/hooks.dart:312:13)
flutter: #59     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #60     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-06-24T19:11:36.524402] [FlutterError] 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: RenderPointerListener#03ac4 relayoutBoundary=up4 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: RenderPointerListener#03ac4 relayoutBoundary=up4 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      RenderBox.size (package:flutter/src/rendering/box.dart:2176:12)
flutter: #3      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:65)
flutter: #4      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #5      ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #6      RenderFlex._computeSizes (package:flutter/src/rendering/flex.dart:1079:71)
flutter: #7      RenderFlex.performLayout (package:flutter/src/rendering/flex.dart:1121:32)
flutter: #8      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #9      RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #10     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #11     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #12     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #13     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #14     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #15     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #16     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #17     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #18     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #19     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #20     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #21     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #22     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #23     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #24     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #25     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #26     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #27     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #28     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #29     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #30     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #31     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #32     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #33     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #34     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #35     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #36     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #37     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #38     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #39     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #40     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #41     RenderOffstage.performLayout (package:flutter/src/rendering/proxy_box.dart:3720:14)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #44     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #45     _RenderTheaterMixin.layoutChild (package:flutter/src/widgets/overlay.dart:1016:13)
flutter: #46     _RenderTheater.performLayout (package:flutter/src/widgets/overlay.dart:1328:9)
flutter: #47     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #48     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #49     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #50     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #51     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #52     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #53     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #54     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #55     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #56     _invoke (dart:ui/hooks.dart:312:13)
flutter: #57     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #58     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-06-24T19:11:36.525655] [FlutterError] 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: _RenderScrollSemantics#5f57a relayoutBoundary=up3 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: _RenderScrollSemantics#5f57a relayoutBoundary=up3 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      RenderBox.size (package:flutter/src/rendering/box.dart:2176:12)
flutter: #3      ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:62:18)
flutter: #4      RenderFlex._computeSizes (package:flutter/src/rendering/flex.dart:1079:71)
flutter: #5      RenderFlex.performLayout (package:flutter/src/rendering/flex.dart:1121:32)
flutter: #6      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #7      RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #8      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #9      MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #10     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #11     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #12     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #13     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #14     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #15     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #16     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #17     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #18     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #19     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #20     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #21     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #22     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #23     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #24     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #25     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #26     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #27     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #28     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #29     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #30     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #31     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #32     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #33     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #34     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #35     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #36     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #37     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #38     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #39     RenderOffstage.performLayout (package:flutter/src/rendering/proxy_box.dart:3720:14)
flutter: #40     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #41     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     _RenderTheaterMixin.layoutChild (package:flutter/src/widgets/overlay.dart:1016:13)
flutter: #44     _RenderTheater.performLayout (package:flutter/src/widgets/overlay.dart:1328:9)
flutter: #45     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #46     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #47     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #48     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #49     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #50     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #51     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #52     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #53     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #54     _invoke (dart:ui/hooks.dart:312:13)
flutter: #55     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #56     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-06-24T19:11:36.527677] [FlutterError] 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: RenderFlex#206ad relayoutBoundary=up2 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: RenderFlex#206ad relayoutBoundary=up2 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      RenderBox.size (package:flutter/src/rendering/box.dart:2176:12)
flutter: #3      RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:243:35)
flutter: #4      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #5      MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #6      _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #7      MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #8      RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #9      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #10     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #11     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #12     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #13     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #14     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #15     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #16     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #17     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #18     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #19     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #20     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #21     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #22     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #23     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #24     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #25     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #26     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #27     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #28     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #29     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #30     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #31     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #32     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #33     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #34     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #35     RenderOffstage.performLayout (package:flutter/src/rendering/proxy_box.dart:3720:14)
flutter: #36     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #37     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #38     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #39     _RenderTheaterMixin.layoutChild (package:flutter/src/widgets/overlay.dart:1016:13)
flutter: #40     _RenderTheater.performLayout (package:flutter/src/widgets/overlay.dart:1328:9)
flutter: #41     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #42     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #43     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #44     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #45     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #46     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #47     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #48     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #49     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #50     _invoke (dart:ui/hooks.dart:312:13)
flutter: #51     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #52     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-06-24T19:11:36.529881] [FlutterError] 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: RenderPadding#0e823 relayoutBoundary=up1 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: RenderPadding#0e823 relayoutBoundary=up1 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      RenderBox.size (package:flutter/src/rendering/box.dart:2176:12)
flutter: #3      MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:179:18)
flutter: #4      _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #5      MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #6      RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #7      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #8      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #9      RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #10     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #11     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #12     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #13     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #14     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #15     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #16     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #17     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #18     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #19     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #20     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #21     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #22     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #23     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #24     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #25     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #26     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #27     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #28     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #29     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #30     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #31     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #32     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #33     RenderOffstage.performLayout (package:flutter/src/rendering/proxy_box.dart:3720:14)
flutter: #34     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #35     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #36     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #37     _RenderTheaterMixin.layoutChild (package:flutter/src/widgets/overlay.dart:1016:13)
flutter: #38     _RenderTheater.performLayout (package:flutter/src/widgets/overlay.dart:1328:9)
flutter: #39     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #40     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #41     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #42     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #43     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #44     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #45     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #46     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #47     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #48     _invoke (dart:ui/hooks.dart:312:13)
flutter: #49     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #50     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-06-24T19:11:36.558761] [PerformanceMonitoringService] Slow frame detected {"duration_ms":450}
flutter: ❌ ERROR [2025-06-24T19:11:36.575086] [FlutterError] 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: _RenderSingleChildViewport#d7058 relayoutBoundary=up9 NEEDS-PAINT 'package:flutter/src/rendering/box.dart': Failed assertion: line 2176 pos 12: 'hasSize': RenderBox was not laid out: _RenderSingleChildViewport#d7058 relayoutBoundary=up9 NEEDS-PAINT
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      RenderBox.size (package:flutter/src/rendering/box.dart:2176:12)
flutter: #3      RenderBox.paintBounds (package:flutter/src/rendering/box.dart:2916:41)
flutter: #4      PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:176:56)
flutter: #5      PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #6      PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #7      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #8      RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #9      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #10     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #11     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #12     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #13     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #14     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #15     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #16     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #17     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #18     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #19     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #20     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #21     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #22     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #23     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #24     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #25     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #26     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #27     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #28     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #29     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #30     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #31     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #32     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #33     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #34     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #35     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #36     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #37     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #38     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #39     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #40     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #41     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #42     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #43     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #44     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #45     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #46     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #47     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #48     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #49     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #50     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #51     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #52     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #53     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #54     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #55     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #56     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #57     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #58     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #59     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #60     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #61     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #64     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #65     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #66     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #67     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #68     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #69     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #70     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #71     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #72     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #73     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #74     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #75     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #76     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #79     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #80     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #81     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #82     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #83     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #84     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #85     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #86     RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #87     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #88     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #89     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #90     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #91     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #92     _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #93     _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #98     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #99     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #102    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #103    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #104    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #105    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #106    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #107    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #108    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #109    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #110    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #111    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #112    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #113    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #114    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #115    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #116    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #117    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #118    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #119    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #120    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #121    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #122    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #123    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #124    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #125    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #126    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #127    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #128    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #129    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #130    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #131    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #132    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #133    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #134    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #135    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #136    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #137    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #138    _invoke (dart:ui/hooks.dart:312:13)
flutter: #139    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #140    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-06-24T19:11:36.605082] [FlutterError] Null check operator used on a null value Null check operator used on a null value
flutter: Stack trace:
flutter: #0      RenderViewportBase.visitChildrenForSemantics.<anonymous closure> (package:flutter/src/rendering/viewport.dart:321:56)
flutter: #1      WhereIterator.moveNext (dart:_internal/iterable.dart:461:13)
flutter: #2      Iterable.forEach (dart:core/iterable.dart:347:23)
flutter: #3      RenderViewportBase.visitChildrenForSemantics (package:flutter/src/rendering/viewport.dart:322:10)
flutter: #4      RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #5      RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #6      RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #7      RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #8      RenderIgnorePointer.visitChildrenForSemantics (package:flutter/src/rendering/proxy_box.dart:3601:11)
flutter: #9      RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #10     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #11     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #12     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #13     RenderSemanticsAnnotations.visitChildrenForSemantics (package:flutter/src/rendering/proxy_box.dart:4275:11)
flutter: #14     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #15     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #16     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #17     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #18     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #19     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #20     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #21     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #22     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #23     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #24     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #25     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #26     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #27     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #28     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #29     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #30     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #31     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #32     ContainerRenderObjectMixin.visitChildren (package:flutter/src/rendering/object.dart:4474:14)
flutter: #33     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #34     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #35     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #36     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #37     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #38     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #39     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #40     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #41     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #42     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #43     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #44     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #45     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #46     RenderIgnorePointer.visitChildrenForSemantics (package:flutter/src/rendering/proxy_box.dart:3601:11)
flutter: #47     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #48     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #49     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #50     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #51     RenderSemanticsAnnotations.visitChildrenForSemantics (package:flutter/src/rendering/proxy_box.dart:4275:11)
flutter: #52     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #53     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #54     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #55     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #56     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #57     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #58     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #59     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #60     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #61     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #62     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #63     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #64     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #65     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #66     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #67     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #68     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #69     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #70     ContainerRenderObjectMixin.visitChildren (package:flutter/src/rendering/object.dart:4474:14)
flutter: #71     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #72     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #73     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #74     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #75     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #76     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #77     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #78     ContainerRenderObjectMixin.visitChildren (package:flutter/src/rendering/object.dart:4474:14)
flutter: #79     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #80     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #81     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #82     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #83     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #84     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #85     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #86     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #87     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #88     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #89     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #90     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #91     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #92     RenderSemanticsAnnotations.visitChildrenForSemantics (package:flutter/src/rendering/proxy_box.dart:4275:11)
flutter: #93     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #94     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #95     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #96     RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #97     RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #98     RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #99     RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #100    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #101    RenderIgnorePointer.visitChildrenForSemantics (package:flutter/src/rendering/proxy_box.dart:3601:11)
flutter: #102    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #103    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #104    ContainerRenderObjectMixin.visitChildren (package:flutter/src/rendering/object.dart:4474:14)
flutter: #105    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #106    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #107    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #108    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #109    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #110    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #111    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #112    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #113    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #114    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #115    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #116    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #117    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #118    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #119    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #120    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #121    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #122    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #123    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #124    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #125    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #126    RenderSemanticsAnnotations.visitChildrenForSemantics (package:flutter/src/rendering/proxy_box.dart:4275:11)
flutter: #127    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #128    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #129    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #130    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #131    RenderOffstage.visitChildrenForSemantics (package:flutter/src/rendering/proxy_box.dart:3750:11)
flutter: #132    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #133    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #134    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #135    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #136    RenderSemanticsAnnotations.visitChildrenForSemantics (package:flutter/src/rendering/proxy_box.dart:4275:11)
flutter: #137    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #138    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #139    _RenderTheater.visitChildrenForSemantics (package:flutter/src/widgets/overlay.dart:1397:14)
flutter: #140    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #141    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #142    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #143    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #144    RenderAbsorbPointer.visitChildrenForSemantics (package:flutter/src/rendering/proxy_box.dart:3859:11)
flutter: #145    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #146    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #147    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #148    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #149    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #150    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #151    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #152    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #153    RenderSemanticsAnnotations.visitChildrenForSemantics (package:flutter/src/rendering/proxy_box.dart:4275:11)
flutter: #154    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #155    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #156    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #157    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #158    RenderSemanticsAnnotations.visitChildrenForSemantics (package:flutter/src/rendering/proxy_box.dart:4275:11)
flutter: #159    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #160    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #161    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #162    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #163    RenderSemanticsAnnotations.visitChildrenForSemantics (package:flutter/src/rendering/proxy_box.dart:4275:11)
flutter: #164    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #165    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #166    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #167    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #168    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #169    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #170    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #171    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #172    RenderSemanticsAnnotations.visitChildrenForSemantics (package:flutter/src/rendering/proxy_box.dart:4275:11)
flutter: #173    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #174    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #175    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #176    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #177    RenderSemanticsAnnotations.visitChildrenForSemantics (package:flutter/src/rendering/proxy_box.dart:4275:11)
flutter: #178    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #179    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #180    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #181    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #182    RenderSemanticsAnnotations.visitChildrenForSemantics (package:flutter/src/rendering/proxy_box.dart:4275:11)
flutter: #183    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #184    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #185    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #186    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #187    RenderSemanticsAnnotations.visitChildrenForSemantics (package:flutter/src/rendering/proxy_box.dart:4275:11)
flutter: #188    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #189    RenderObject._getSemanticsForParent.<anonymous closure> (package:flutter/src/rendering/object.dart:3765:61)
flutter: #190    RenderObjectWithChildMixin.visitChildren (package:flutter/src/rendering/object.dart:4183:14)
flutter: #191    RenderObject.visitChildrenForSemantics (package:flutter/src/rendering/object.dart:3906:5)
flutter: #192    RenderObject._getSemanticsForParent (package:flutter/src/rendering/object.dart:3763:5)
flutter: #193    RenderObject._updateSemantics (package:flutter/src/rendering/object.dart:3715:41)
flutter: #194    PipelineOwner.flushSemantics (package:flutter/src/rendering/object.dart:1313:16)
flutter: #195    PipelineOwner.flushSemantics (package:flutter/src/rendering/object.dart:1318:15)
flutter: #196    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:616:25)
flutter: #197    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #198    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #199    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #200    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #201    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #202    _invoke (dart:ui/hooks.dart:312:13)
flutter: #203    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #204    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-06-24T19:11:36.608591] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-24T19:11:36.641626] [PerformanceMonitoringService] Slow frame detected {"duration_ms":32}
flutter: 🐛 DEBUG [2025-06-24T19:11:37.587061] [PerformanceMonitoringService] Slow frame detected {"duration_ms":112}
flutter: 🐛 DEBUG [2025-06-24T19:11:37.607321] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: 🐛 DEBUG [2025-06-24T19:11:38.684154] [PerformanceMonitoringService] Slow frame detected {"duration_ms":109}
flutter: 🐛 DEBUG [2025-06-24T19:11:38.706470] [PerformanceMonitoringService] Slow frame detected {"duration_ms":23}
flutter: 🐛 DEBUG [2025-06-24T19:11:39.760854] [PerformanceMonitoringService] Slow frame detected {"duration_ms":88}
flutter: 🐛 DEBUG [2025-06-24T19:11:39.789401] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-06-24T19:11:40.843688] [PerformanceMonitoringService] Slow frame detected {"duration_ms":88}
flutter: 🐛 DEBUG [2025-06-24T19:11:40.872558] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-06-24T19:11:41.932404] [PerformanceMonitoringService] Slow frame detected {"duration_ms":94}
flutter: 🐛 DEBUG [2025-06-24T19:11:41.954295] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-06-24T19:11:42.710383] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-06-24T19:11:42.737011] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-06-24T19:11:43.003209] [PerformanceMonitoringService] Slow frame detected {"duration_ms":82}
flutter: 🐛 DEBUG [2025-06-24T19:11:43.020504] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: 🐛 DEBUG [2025-06-24T19:11:44.072270] [PerformanceMonitoringService] Slow frame detected {"duration_ms":69}
flutter: ⚠️ WARNING [2025-06-24T19:11:44.232661] [PerformanceMonitoringService] High memory usage detected {"memory_mb":182.0}
flutter: 🐛 DEBUG [2025-06-24T19:11:45.128439] [PerformanceMonitoringService] Slow frame detected {"duration_ms":59}
flutter: 🐛 DEBUG [2025-06-24T19:11:45.153314] [PerformanceMonitoringService] Slow frame detected {"duration_ms":23}
flutter: 🐛 DEBUG [2025-06-24T19:11:46.188242] [PerformanceMonitoringService] Slow frame detected {"duration_ms":69}
flutter: 🐛 DEBUG [2025-06-24T19:11:47.242564] [PerformanceMonitoringService] Slow frame detected {"duration_ms":57}
flutter: 🐛 DEBUG [2025-06-24T19:11:47.267781] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: 🐛 DEBUG [2025-06-24T19:11:48.307282] [PerformanceMonitoringService] Slow frame detected {"duration_ms":73}
flutter: 🐛 DEBUG [2025-06-24T19:11:48.333946] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-06-24T19:11:49.349389] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-06-24T19:11:49.368032] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: 🐛 DEBUG [2025-06-24T19:11:50.387816] [PerformanceMonitoringService] Slow frame detected {"duration_ms":38}
flutter: 🐛 DEBUG [2025-06-24T19:11:50.417154] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: ⚠️ WARNING [2025-06-24T19:11:59.225952] [PerformanceMonitoringService] High memory usage detected {"memory_mb":175.0}
flutter: ⚠️ WARNING [2025-06-24T19:12:14.223446] [PerformanceMonitoringService] High memory usage detected {"memory_mb":173.0}
flutter: 🐛 DEBUG [2025-06-24T19:12:21.594594] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T19:12:25.511096] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: ⚠️ WARNING [2025-06-24T19:12:29.223187] [PerformanceMonitoringService] High memory usage detected {"memory_mb":173.0}
flutter: 🐛 DEBUG [2025-06-24T19:12:31.177694] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T19:12:32.676879] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-06-24T19:12:32.694151] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: 🐛 DEBUG [2025-06-24T19:12:35.277448] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-24T19:12:44.177449] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-06-24T19:12:44.210616] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-24T19:12:44.225576] [PerformanceMonitoringService] High memory usage detected {"memory_mb":175.0}
flutter: 🐛 DEBUG [2025-06-24T19:12:44.631776] [PerformanceMonitoringService] Slow frame detected {"duration_ms":38}
flutter: 🐛 DEBUG [2025-06-24T19:12:44.660901] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-06-24T19:12:44.714842] [PerformanceMonitoringService] Slow frame detected {"duration_ms":54}
flutter: 🐛 DEBUG [2025-06-24T19:12:44.744711] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}